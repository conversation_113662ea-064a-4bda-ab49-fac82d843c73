<UserControl x:Class="ClothingStoreApp.Views.CustomersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:viewmodels="clr-namespace:ClothingStoreApp.ViewModels"
             FlowDirection="RightToLeft">
    
    <UserControl.DataContext>
        <viewmodels:CustomersViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header and Search -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Title -->
            <TextBlock Grid.Row="0" 
                     Text="إدارة العملاء" 
                     FontSize="28" 
                     FontWeight="Bold"
                     Foreground="#FF2196F3"
                     Margin="0,0,0,15"/>

            <!-- Search and Actions -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <Border Grid.Column="0" 
                      Background="White" 
                      BorderBrush="#FFDDDDDD" 
                      BorderThickness="1" 
                      CornerRadius="5"
                      Padding="10"
                      Margin="0,0,10,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0"
                               Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                               FontSize="14"
                               BorderThickness="0"
                               Background="Transparent"
                               VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Column="0"
                                 Text="البحث في العملاء..."
                                 FontSize="14"
                                 Foreground="Gray"
                                 VerticalAlignment="Center"
                                 IsHitTestVisible="False">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding SearchText}" Value="">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                        
                        <TextBlock Grid.Column="1" 
                                 Text="🔍" 
                                 FontSize="16" 
                                 VerticalAlignment="Center"
                                 Foreground="Gray"/>
                    </Grid>
                </Border>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="➕ عميل جديد"
                          Command="{Binding AddCustomerCommand}"
                          Height="40" Width="120"
                          Background="#FF4CAF50" Foreground="White"
                          BorderThickness="0"
                          Margin="5,0"/>
                    
                    <Button Content="✏️ تعديل"
                          Command="{Binding EditCustomerCommand}"
                          Height="40" Width="80"
                          Background="#FF2196F3" Foreground="White"
                          BorderThickness="0"
                          Margin="5,0"/>
                    
                    <Button Content="🗑️ حذف"
                          Command="{Binding DeleteCustomerCommand}"
                          Height="40" Width="80"
                          Background="#FFF44336" Foreground="White"
                          BorderThickness="0"
                          Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Customers List -->
            <Border Grid.Column="0" 
                  Background="White" 
                  BorderBrush="#FFDDDDDD" 
                  BorderThickness="1" 
                  CornerRadius="5"
                  Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- List Header -->
                    <Border Grid.Row="0" 
                          Background="#FFF5F5F5" 
                          BorderBrush="#FFDDDDDD" 
                          BorderThickness="0,0,0,1"
                          Padding="15,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="اسم العميل" FontWeight="Bold"/>
                            <TextBlock Grid.Column="1" Text="رقم الهاتف" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="إجمالي المشتريات" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="نقاط الولاء" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Grid>
                    </Border>

                    <!-- Customers DataGrid -->
                    <DataGrid Grid.Row="1"
                            ItemsSource="{Binding FilteredCustomers}"
                            SelectedItem="{Binding SelectedCustomer}"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="None"
                            BorderThickness="0"
                            Background="White"
                            AlternatingRowBackground="#FFF9F9F9">
                        
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Header="اسم العميل" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Margin="5">
                                            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                            <TextBlock Text="{Binding Email}" 
                                                     Foreground="Gray" 
                                                     FontSize="12"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120"/>
                            <DataGridTextColumn Header="إجمالي المشتريات" Binding="{Binding TotalPurchases, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                            <DataGridTextColumn Header="نقاط الولاء" Binding="{Binding LoyaltyPoints}" Width="100"/>
                        </DataGrid.Columns>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Height" Value="50"/>
                            </Style>
                        </DataGrid.RowStyle>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- Customer Details/Form -->
            <Border Grid.Column="1" 
                  Background="White" 
                  BorderBrush="#FFDDDDDD" 
                  BorderThickness="1" 
                  CornerRadius="5">
                
                <Grid>
                    <!-- Customer Form -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                                Visibility="{Binding IsCustomerFormVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Margin="20">
                            <TextBlock Text="{Binding IsAddingCustomer, Converter={StaticResource AddEditTitleConverter}, ConverterParameter=Customer}"
                                     FontSize="18"
                                     FontWeight="Bold"
                                     Foreground="#FF2196F3"
                                     Margin="0,0,0,20"/>

                            <!-- Customer Name -->
                            <Grid Margin="0,0,0,15">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding CustomerName}"
                                       Height="35"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"/>
                            </Grid>

                            <!-- Phone -->
                            <Grid Margin="0,0,0,15">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="رقم الهاتف:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding CustomerPhone}"
                                       Height="35"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"/>
                            </Grid>

                            <!-- Email -->
                            <Grid Margin="0,0,0,15">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="البريد الإلكتروني:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding CustomerEmail}"
                                       Height="35"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"/>
                            </Grid>

                            <!-- Address -->
                            <Grid Margin="0,0,0,15">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="العنوان:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding CustomerAddress}"
                                       Height="60"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"
                                       TextWrapping="Wrap"
                                       AcceptsReturn="True"/>
                            </Grid>

                            <!-- Date of Birth -->
                            <Grid Margin="0,0,0,20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="تاريخ الميلاد:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <DatePicker Grid.Row="1" 
                                          SelectedDate="{Binding CustomerDateOfBirth}"
                                          Height="35"
                                          BorderBrush="#FFDDDDDD"
                                          BorderThickness="1"/>
                            </Grid>

                            <!-- Action Buttons -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <Button Grid.Column="0"
                                      Content="💾 حفظ"
                                      Command="{Binding SaveCustomerCommand}"
                                      Height="40"
                                      Background="#FF4CAF50" Foreground="White"
                                      BorderThickness="0"/>
                                
                                <Button Grid.Column="2"
                                      Content="❌ إلغاء"
                                      Command="{Binding CancelCustomerCommand}"
                                      Height="40"
                                      Background="#FF757575" Foreground="White"
                                      BorderThickness="0"/>
                            </Grid>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Customer Details (when not editing) -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                                Visibility="{Binding IsCustomerFormVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <StackPanel Margin="20">
                            <TextBlock Text="تفاصيل العميل" 
                                     FontSize="18" 
                                     FontWeight="Bold"
                                     Foreground="#FF2196F3"
                                     Margin="0,0,0,20"/>

                            <Grid Visibility="{Binding SelectedCustomer, Converter={StaticResource NullToVisibilityConverter}}">
                                <!-- Customer Info -->
                                <StackPanel>
                                    <TextBlock Text="{Binding SelectedCustomer.Name}" 
                                             FontSize="16" 
                                             FontWeight="Bold"
                                             Margin="0,0,0,10"/>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="الهاتف: " FontWeight="Bold"/>
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedCustomer.Phone}"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="البريد: " FontWeight="Bold"/>
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedCustomer.Email}"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="العنوان: " FontWeight="Bold"/>
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedCustomer.Address}" TextWrapping="Wrap"/>
                                    </Grid>

                                    <Separator Margin="0,15"/>

                                    <!-- Statistics -->
                                    <TextBlock Text="الإحصائيات" 
                                             FontSize="16" 
                                             FontWeight="Bold"
                                             Foreground="#FF2196F3"
                                             Margin="0,0,0,10"/>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="إجمالي المشتريات: " FontWeight="Bold"/>
                                        <TextBlock Grid.Column="1" Text="{Binding CustomerTotalPurchases, StringFormat='{}{0:N2} ج.م'}" Foreground="#FF4CAF50"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="نقاط الولاء: " FontWeight="Bold"/>
                                        <TextBlock Grid.Column="1" Text="{Binding CustomerLoyaltyPoints}" Foreground="#FFFF9800"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,15">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="آخر زيارة: " FontWeight="Bold"/>
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedCustomer.LastVisit, StringFormat='{}{0:yyyy/MM/dd}'}"/>
                                    </Grid>

                                    <!-- Recent Sales -->
                                    <TextBlock Text="المشتريات الأخيرة" 
                                             FontSize="14" 
                                             FontWeight="Bold"
                                             Foreground="#FF2196F3"
                                             Margin="0,0,0,10"/>

                                    <ListBox ItemsSource="{Binding CustomerSales}"
                                           MaxHeight="200"
                                           BorderThickness="1"
                                           BorderBrush="#FFDDDDDD">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Border Padding="10,5" BorderBrush="#FFEEEEEE" BorderThickness="0,0,0,1">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        
                                                        <TextBlock Grid.Column="0" 
                                                                 Text="{Binding SaleDate, StringFormat='{}{0:yyyy/MM/dd}'}" 
                                                                 FontSize="12"/>
                                                        <TextBlock Grid.Column="1" 
                                                                 Text="{Binding TotalAmount, StringFormat='{}{0:N2} ج.م'}" 
                                                                 FontSize="12" 
                                                                 FontWeight="Bold"/>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                    </ListBox>
                                </StackPanel>
                            </Grid>

                            <TextBlock Text="اختر عميل لعرض التفاصيل" 
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     Foreground="Gray"
                                     FontSize="16"
                                     Visibility="{Binding SelectedCustomer, Converter={StaticResource NullToVisibilityConverter}, ConverterParameter=Inverse}"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
