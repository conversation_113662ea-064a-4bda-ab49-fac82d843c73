using System.Collections.ObjectModel;
using System.Windows.Input;
using ClothingStoreApp.Models;
using ClothingStoreApp.Services;
using ClothingStoreApp.Data;

namespace ClothingStoreApp.ViewModels
{
    public class CustomersViewModel : BaseViewModel
    {
        private readonly CustomerService _customerService;
        private readonly SalesService _salesService;
        private ObservableCollection<Customer> _customers;
        private ObservableCollection<Customer> _filteredCustomers;
        private Customer? _selectedCustomer;
        private string _searchText = string.Empty;
        private bool _isAddingCustomer = false;
        private bool _isEditingCustomer = false;

        // New/Edit Customer Properties
        private string _customerName = string.Empty;
        private string _customerPhone = string.Empty;
        private string _customerEmail = string.Empty;
        private string _customerAddress = string.Empty;
        private DateTime _customerDateOfBirth = DateTime.Today.AddYears(-25);

        // Customer statistics
        private ObservableCollection<Sale> _customerSales;
        private decimal _customerTotalPurchases = 0;
        private int _customerLoyaltyPoints = 0;

        public CustomersViewModel()
        {
            try
            {
                var context = new ClothingStoreContext();
                // Ensure database is created
                context.Database.EnsureCreated();
                _customerService = new CustomerService(context);
                _salesService = new SalesService(context, new ProductService(context));
                
                _customers = new ObservableCollection<Customer>();
                _filteredCustomers = new ObservableCollection<Customer>();
                _customerSales = new ObservableCollection<Sale>();

                // Initialize commands
                LoadCustomersCommand = new RelayCommand(async () => await LoadCustomersAsync());
                AddCustomerCommand = new RelayCommand(() => StartAddCustomer());
                EditCustomerCommand = new RelayCommand(() => StartEditCustomer(), () => SelectedCustomer != null);
                DeleteCustomerCommand = new RelayCommand(async () => await DeleteCustomerAsync(), () => SelectedCustomer != null);
                SaveCustomerCommand = new RelayCommand(async () => await SaveCustomerAsync());
                CancelCustomerCommand = new RelayCommand(() => CancelCustomerOperation());
                SearchCommand = new RelayCommand(() => FilterCustomers());
                LoadCustomerSalesCommand = new RelayCommand(async () => await LoadCustomerSalesAsync(), () => SelectedCustomer != null);

                // Load customers on startup
                _ = Task.Run(LoadCustomersAsync);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing CustomersViewModel: {ex.Message}");
            }
        }

        #region Properties

        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set => SetProperty(ref _customers, value);
        }

        public ObservableCollection<Customer> FilteredCustomers
        {
            get => _filteredCustomers;
            set => SetProperty(ref _filteredCustomers, value);
        }

        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                SetProperty(ref _selectedCustomer, value);
                if (value != null)
                {
                    CustomerTotalPurchases = value.TotalPurchases;
                    CustomerLoyaltyPoints = value.LoyaltyPoints;
                    _ = Task.Run(LoadCustomerSalesAsync);
                }
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                FilterCustomers();
            }
        }

        public bool IsAddingCustomer
        {
            get => _isAddingCustomer;
            set => SetProperty(ref _isAddingCustomer, value);
        }

        public bool IsEditingCustomer
        {
            get => _isEditingCustomer;
            set => SetProperty(ref _isEditingCustomer, value);
        }

        public bool IsCustomerFormVisible => IsAddingCustomer || IsEditingCustomer;

        // Customer Form Properties
        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }

        public string CustomerPhone
        {
            get => _customerPhone;
            set => SetProperty(ref _customerPhone, value);
        }

        public string CustomerEmail
        {
            get => _customerEmail;
            set => SetProperty(ref _customerEmail, value);
        }

        public string CustomerAddress
        {
            get => _customerAddress;
            set => SetProperty(ref _customerAddress, value);
        }

        public DateTime CustomerDateOfBirth
        {
            get => _customerDateOfBirth;
            set => SetProperty(ref _customerDateOfBirth, value);
        }

        // Customer Statistics
        public ObservableCollection<Sale> CustomerSales
        {
            get => _customerSales;
            set => SetProperty(ref _customerSales, value);
        }

        public decimal CustomerTotalPurchases
        {
            get => _customerTotalPurchases;
            set => SetProperty(ref _customerTotalPurchases, value);
        }

        public int CustomerLoyaltyPoints
        {
            get => _customerLoyaltyPoints;
            set => SetProperty(ref _customerLoyaltyPoints, value);
        }

        #endregion

        #region Commands

        public ICommand LoadCustomersCommand { get; }
        public ICommand AddCustomerCommand { get; }
        public ICommand EditCustomerCommand { get; }
        public ICommand DeleteCustomerCommand { get; }
        public ICommand SaveCustomerCommand { get; }
        public ICommand CancelCustomerCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand LoadCustomerSalesCommand { get; }

        #endregion

        #region Methods

        private async Task LoadCustomersAsync()
        {
            try
            {
                if (_customerService == null) return;

                var customers = await _customerService.GetAllCustomersAsync();
                
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }
                
                FilterCustomers();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading customers: {ex.Message}");
            }
        }

        private void FilterCustomers()
        {
            FilteredCustomers.Clear();
            
            var filtered = string.IsNullOrWhiteSpace(SearchText) 
                ? Customers 
                : Customers.Where(c => 
                    c.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    c.Phone.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    c.Email.Contains(SearchText, StringComparison.OrdinalIgnoreCase));

            foreach (var customer in filtered)
            {
                FilteredCustomers.Add(customer);
            }
        }

        private void StartAddCustomer()
        {
            ClearCustomerForm();
            IsAddingCustomer = true;
            OnPropertyChanged(nameof(IsCustomerFormVisible));
        }

        private void StartEditCustomer()
        {
            if (SelectedCustomer == null) return;

            LoadCustomerToForm(SelectedCustomer);
            IsEditingCustomer = true;
            OnPropertyChanged(nameof(IsCustomerFormVisible));
        }

        private async Task SaveCustomerAsync()
        {
            try
            {
                if (_customerService == null) return;

                var customer = IsEditingCustomer ? SelectedCustomer : new Customer();
                if (customer == null) return;

                // Update customer properties
                customer.Name = CustomerName;
                customer.Phone = CustomerPhone;
                customer.Email = CustomerEmail;
                customer.Address = CustomerAddress;
                customer.DateOfBirth = CustomerDateOfBirth;

                if (IsAddingCustomer)
                {
                    await _customerService.AddCustomerAsync(customer);
                    Customers.Add(customer);
                }
                else
                {
                    await _customerService.UpdateCustomerAsync(customer);
                }

                FilterCustomers();
                CancelCustomerOperation();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving customer: {ex.Message}");
            }
        }

        private async Task DeleteCustomerAsync()
        {
            if (SelectedCustomer == null || _customerService == null) return;

            try
            {
                await _customerService.DeleteCustomerAsync(SelectedCustomer.Id);
                Customers.Remove(SelectedCustomer);
                FilterCustomers();
                SelectedCustomer = null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting customer: {ex.Message}");
            }
        }

        private void CancelCustomerOperation()
        {
            IsAddingCustomer = false;
            IsEditingCustomer = false;
            ClearCustomerForm();
            OnPropertyChanged(nameof(IsCustomerFormVisible));
        }

        private void ClearCustomerForm()
        {
            CustomerName = string.Empty;
            CustomerPhone = string.Empty;
            CustomerEmail = string.Empty;
            CustomerAddress = string.Empty;
            CustomerDateOfBirth = DateTime.Today.AddYears(-25);
        }

        private void LoadCustomerToForm(Customer customer)
        {
            CustomerName = customer.Name;
            CustomerPhone = customer.Phone;
            CustomerEmail = customer.Email;
            CustomerAddress = customer.Address;
            CustomerDateOfBirth = customer.DateOfBirth;
        }

        private async Task LoadCustomerSalesAsync()
        {
            try
            {
                if (SelectedCustomer == null || _salesService == null) return;

                // Get sales for the selected customer
                var allSales = await _salesService.GetSalesByDateRangeAsync(DateTime.Today.AddYears(-1), DateTime.Today);
                var customerSales = allSales.Where(s => s.CustomerId == SelectedCustomer.Id).ToList();

                CustomerSales.Clear();
                foreach (var sale in customerSales.OrderByDescending(s => s.SaleDate))
                {
                    CustomerSales.Add(sale);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading customer sales: {ex.Message}");
            }
        }

        #endregion
    }
}
