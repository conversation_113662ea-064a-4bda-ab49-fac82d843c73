using System.Collections.ObjectModel;
using System.Windows.Input;
using ClothingStoreApp.Models;
using ClothingStoreApp.Services;
using ClothingStoreApp.Data;

namespace ClothingStoreApp.ViewModels
{
    public class MainViewModel : BaseViewModel
    {
        private readonly ProductService _productService;
        private readonly SalesService _salesService;
        private readonly CustomerService _customerService;

        private string _currentView = "Dashboard";
        private decimal _todaysSales;
        private int _todaysSalesCount;
        private int _lowStockCount;
        private int _totalCustomers;

        public MainViewModel()
        {
            try
            {
                var context = new ClothingStoreContext();
                // Ensure database is created
                context.Database.EnsureCreated();
                _productService = new ProductService(context);
                _salesService = new SalesService(context, _productService);
                _customerService = new CustomerService(context);

                // Initialize commands
                ShowDashboardCommand = new RelayCommand(() => CurrentView = "Dashboard");
                ShowProductsCommand = new RelayCommand(() => CurrentView = "Products");
                ShowSalesCommand = new RelayCommand(() => CurrentView = "Sales");
                ShowCustomersCommand = new RelayCommand(() => CurrentView = "Customers");
                ShowReportsCommand = new RelayCommand(() => CurrentView = "Reports");
                ShowSettingsCommand = new RelayCommand(() => CurrentView = "Settings");

                // Load data asynchronously after initialization
                _ = Task.Run(LoadDashboardDataAsync);
            }
            catch (Exception ex)
            {
                // Initialize commands even if database fails
                ShowDashboardCommand = new RelayCommand(() => CurrentView = "Dashboard");
                ShowProductsCommand = new RelayCommand(() => CurrentView = "Products");
                ShowSalesCommand = new RelayCommand(() => CurrentView = "Sales");
                ShowCustomersCommand = new RelayCommand(() => CurrentView = "Customers");
                ShowReportsCommand = new RelayCommand(() => CurrentView = "Reports");
                ShowSettingsCommand = new RelayCommand(() => CurrentView = "Settings");

                System.Diagnostics.Debug.WriteLine($"Error initializing MainViewModel: {ex.Message}");
            }
        }

        public string CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        public decimal TodaysSales
        {
            get => _todaysSales;
            set => SetProperty(ref _todaysSales, value);
        }

        public int TodaysSalesCount
        {
            get => _todaysSalesCount;
            set => SetProperty(ref _todaysSalesCount, value);
        }

        public int LowStockCount
        {
            get => _lowStockCount;
            set => SetProperty(ref _lowStockCount, value);
        }

        public int TotalCustomers
        {
            get => _totalCustomers;
            set => SetProperty(ref _totalCustomers, value);
        }

        public ICommand ShowDashboardCommand { get; }
        public ICommand ShowProductsCommand { get; }
        public ICommand ShowSalesCommand { get; }
        public ICommand ShowCustomersCommand { get; }
        public ICommand ShowReportsCommand { get; }
        public ICommand ShowSettingsCommand { get; }

        private async void LoadDashboardDataAsync()
        {
            try
            {
                if (_salesService == null || _productService == null || _customerService == null)
                {
                    // Set default values if services are not initialized
                    TodaysSales = 0;
                    TodaysSalesCount = 0;
                    LowStockCount = 0;
                    TotalCustomers = 0;
                    return;
                }

                var today = DateTime.Today;
                TodaysSales = await _salesService.GetTotalSalesForDateAsync(today);
                TodaysSalesCount = await _salesService.GetSalesCountForDateAsync(today);

                var lowStockProducts = await _productService.GetLowStockProductsAsync();
                LowStockCount = lowStockProducts.Count;

                var customers = await _customerService.GetAllCustomersAsync();
                TotalCustomers = customers.Count;
            }
            catch (Exception ex)
            {
                // Set default values on error
                TodaysSales = 0;
                TodaysSalesCount = 0;
                LowStockCount = 0;
                TotalCustomers = 0;

                System.Diagnostics.Debug.WriteLine($"Error loading dashboard data: {ex.Message}");
            }
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute();
        }
    }
}
