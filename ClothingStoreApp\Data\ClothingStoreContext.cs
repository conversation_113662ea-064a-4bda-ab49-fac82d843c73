using Microsoft.EntityFrameworkCore;
using ClothingStoreApp.Models;
using System.IO;

namespace ClothingStoreApp.Data
{
    public class ClothingStoreContext : DbContext
    {
        public DbSet<Product> Products { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Sale> Sales { get; set; }
        public DbSet<SaleItem> SaleItems { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                    "ClothingStore", "clothingstore.db");
            
            // Create directory if it doesn't exist
            var directory = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Product
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasIndex(e => e.Barcode).IsUnique();
                entity.Property(e => e.Price).HasPrecision(18, 2);
                entity.Property(e => e.CostPrice).HasPrecision(18, 2);
            });

            // Configure Customer
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasIndex(e => e.Phone);
                entity.Property(e => e.TotalPurchases).HasPrecision(18, 2);
            });

            // Configure Sale
            modelBuilder.Entity<Sale>(entity =>
            {
                entity.Property(e => e.SubTotal).HasPrecision(18, 2);
                entity.Property(e => e.DiscountAmount).HasPrecision(18, 2);
                entity.Property(e => e.TaxAmount).HasPrecision(18, 2);
                entity.Property(e => e.TotalAmount).HasPrecision(18, 2);

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.Sales)
                    .HasForeignKey(d => d.CustomerId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure SaleItem
            modelBuilder.Entity<SaleItem>(entity =>
            {
                entity.Property(e => e.UnitPrice).HasPrecision(18, 2);
                entity.Property(e => e.DiscountAmount).HasPrecision(18, 2);
                entity.Property(e => e.TotalPrice).HasPrecision(18, 2);

                entity.HasOne(d => d.Sale)
                    .WithMany(p => p.SaleItems)
                    .HasForeignKey(d => d.SaleId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SaleItems)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed sample products
            modelBuilder.Entity<Product>().HasData(
                new Product
                {
                    Id = 1,
                    Name = "قميص قطني رجالي",
                    Description = "قميص قطني عالي الجودة للرجال",
                    Price = 250.00m,
                    CostPrice = 150.00m,
                    Quantity = 50,
                    MinimumStock = 10,
                    Category = "قمصان رجالي",
                    Brand = "ماركة محلية",
                    Size = "L",
                    Color = "أبيض",
                    Barcode = "1234567890123",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Product
                {
                    Id = 2,
                    Name = "فستان صيفي نسائي",
                    Description = "فستان صيفي أنيق للنساء",
                    Price = 450.00m,
                    CostPrice = 280.00m,
                    Quantity = 30,
                    MinimumStock = 5,
                    Category = "فساتين نسائي",
                    Brand = "ماركة عالمية",
                    Size = "M",
                    Color = "أزرق",
                    Barcode = "1234567890124",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            );
        }
    }
}
