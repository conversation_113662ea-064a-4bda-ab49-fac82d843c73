# نظام إدارة محل الملابس
## Clothing Store Management System

تطبيق احترافي لإدارة محل الملابس يعمل على جميع أنظمة ويندوز من ويندوز 7 فما فوق.

## الميزات الرئيسية

### 📊 لوحة التحكم
- إحصائيات فورية لمبيعات اليوم
- عدد الفواتير المباعة
- تنبيهات المنتجات قاربت على النفاد
- إجمالي العملاء المسجلين

### 👕 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- دعم الصور والمقاسات والألوان
- نظام الباركود
- تتبع المخزون

### 💰 نقاط البيع (POS)
- واجهة بيع سريعة وسهلة
- دعم الخصومات والضرائب
- طرق دفع متعددة (نقدي، فيزا، ماستركارد)
- طباعة الفواتير

### 👥 إدارة العملاء
- قاعدة بيانات العملاء
- نظام نقاط الولاء
- سجل المشتريات

### 📈 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تحليل الأرباح
- المنتجات الأكثر مبيعاً

## متطلبات التشغيل

- ويندوز 7 أو أحدث
- .NET 8.0 Runtime
- 100 ميجابايت مساحة فارغة

## طريقة التشغيل

### الطريقة الأولى: باستخدام .NET CLI
```bash
dotnet run
```

### الطريقة الثانية: باستخدام ملف التشغيل
انقر مرتين على ملف `run.bat`

### الطريقة الثالثة: تشغيل الملف التنفيذي مباشرة
```bash
.\bin\Debug\net8.0-windows\ClothingStoreApp.exe
```

## البناء والتطوير

### بناء المشروع
```bash
dotnet build
```

### تنظيف المشروع
```bash
dotnet clean
```

### إنشاء نسخة للتوزيع
```bash
dotnet publish -c Release -r win-x64 --self-contained
```

## هيكل المشروع

```
ClothingStoreApp/
├── Models/              # نماذج البيانات
├── ViewModels/          # منطق العرض (MVVM)
├── Views/               # واجهات المستخدم
├── Data/                # قاعدة البيانات
├── Services/            # خدمات البيانات
├── Helpers/             # أدوات مساعدة
└── Resources/           # الموارد والصور
```

## التقنيات المستخدمة

- **WPF** - واجهة المستخدم
- **Entity Framework Core** - قاعدة البيانات
- **SQLite** - قاعدة بيانات محلية
- **MVVM Pattern** - نمط التصميم
- **.NET 8.0** - إطار العمل

## العملة

جميع الأسعار والمبالغ بالجنيه المصري (ج.م)

## الدعم

للدعم الفني أو الاستفسارات، يرجى التواصل معنا.

---

**الإصدار:** 1.0.0  
**تاريخ الإصدار:** 2024  
**المطور:** Clothing Store Management System
