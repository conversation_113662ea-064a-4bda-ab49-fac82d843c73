<UserControl x:Class="ClothingStoreApp.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:viewmodels="clr-namespace:ClothingStoreApp.ViewModels"
             FlowDirection="RightToLeft">
    
    <UserControl.DataContext>
        <viewmodels:ReportsViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                     Text="التقارير والإحصائيات" 
                     FontSize="28" 
                     FontWeight="Bold"
                     Foreground="#FF2196F3"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="🔄 تحديث"
                      Command="{Binding RefreshDataCommand}"
                      Height="40" Width="100"
                      Background="#FF4CAF50" Foreground="White"
                      BorderThickness="0"
                      Margin="5,0"/>
                
                <Button Content="📊 تصدير"
                      Command="{Binding ExportReportCommand}"
                      Height="40" Width="100"
                      Background="#FF2196F3" Foreground="White"
                      BorderThickness="0"
                      Margin="5,0"/>
            </StackPanel>
        </Grid>

        <!-- Date Range and Report Type Selection -->
        <Border Grid.Row="1" 
              Background="White" 
              BorderBrush="#FFDDDDDD" 
              BorderThickness="1" 
              CornerRadius="5"
              Padding="20"
              Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" FontWeight="Bold" Margin="0,0,10,0"/>
                <DatePicker Grid.Column="1" SelectedDate="{Binding StartDate}" Height="35"/>

                <TextBlock Grid.Column="3" Text="إلى تاريخ:" VerticalAlignment="Center" FontWeight="Bold" Margin="0,0,10,0"/>
                <DatePicker Grid.Column="4" SelectedDate="{Binding EndDate}" Height="35"/>

                <TextBlock Grid.Column="6" Text="نوع التقرير:" VerticalAlignment="Center" FontWeight="Bold" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="7" 
                        SelectedItem="{Binding SelectedReportType}"
                        ItemsSource="{Binding ReportTypes}"
                        Height="35"/>

                <Button Grid.Column="9"
                      Content="📈 إنشاء التقرير"
                      Command="{Binding GenerateReportCommand}"
                      Height="35" Width="140"
                      Background="#FF4CAF50" Foreground="White"
                      BorderThickness="0"/>
            </Grid>
        </Border>

        <!-- Reports Content -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Summary Cards -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Total Sales Card -->
                    <Border Grid.Column="0" 
                          Background="#FF4CAF50" 
                          CornerRadius="10"
                          Padding="20"
                          Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="إجمالي المبيعات" 
                                     Foreground="White" 
                                     FontSize="14" 
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalSales, StringFormat='{}{0:N2} ج.م'}" 
                                     Foreground="White" 
                                     FontSize="24" 
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Total Profit Card -->
                    <Border Grid.Column="1" 
                          Background="#FF2196F3" 
                          CornerRadius="10"
                          Padding="20"
                          Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="إجمالي الربح" 
                                     Foreground="White" 
                                     FontSize="14" 
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalProfit, StringFormat='{}{0:N2} ج.م'}" 
                                     Foreground="White" 
                                     FontSize="24" 
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Total Transactions Card -->
                    <Border Grid.Column="2" 
                          Background="#FFFF9800" 
                          CornerRadius="10"
                          Padding="20"
                          Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="عدد المعاملات" 
                                     Foreground="White" 
                                     FontSize="14" 
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalTransactions}" 
                                     Foreground="White" 
                                     FontSize="24" 
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Average Transaction Card -->
                    <Border Grid.Column="3" 
                          Background="#FF9C27B0" 
                          CornerRadius="10"
                          Padding="20">
                        <StackPanel>
                            <TextBlock Text="متوسط المعاملة" 
                                     Foreground="White" 
                                     FontSize="14" 
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding AverageTransactionValue, StringFormat='{}{0:N2} ج.م'}" 
                                     Foreground="White" 
                                     FontSize="24" 
                                     FontWeight="Bold"
                                     HorizontalAlignment="Center"
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- Charts and Tables -->
                <Grid Grid.Row="1" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Daily Sales Chart -->
                    <Border Grid.Column="0" 
                          Background="White" 
                          BorderBrush="#FFDDDDDD" 
                          BorderThickness="1" 
                          CornerRadius="5"
                          Padding="20"
                          Margin="0,0,10,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" 
                                     Text="المبيعات اليومية" 
                                     FontSize="18" 
                                     FontWeight="Bold"
                                     Foreground="#FF2196F3"
                                     Margin="0,0,0,15"/>

                            <!-- Simple chart representation -->
                            <ItemsControl Grid.Row="1" ItemsSource="{Binding DailySalesData}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Padding="5,2" 
                                              BorderBrush="#FFEEEEEE" 
                                              BorderThickness="0,0,0,1">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="100"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="100"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0" 
                                                         Text="{Binding Date, StringFormat='{}{0:MM/dd}'}" 
                                                         FontSize="12"/>
                                                
                                                <Rectangle Grid.Column="1" 
                                                         Height="20" 
                                                         Fill="#FF4CAF50" 
                                                         HorizontalAlignment="Left"
                                                         Width="{Binding TotalSales, Converter={StaticResource SalesWidthConverter}}"
                                                         Margin="10,0"/>
                                                
                                                <TextBlock Grid.Column="2" 
                                                         Text="{Binding TotalSales, StringFormat='{}{0:N0}'}" 
                                                         FontSize="12"
                                                         HorizontalAlignment="Right"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </Grid>
                    </Border>

                    <!-- Top Products -->
                    <Border Grid.Column="1" 
                          Background="White" 
                          BorderBrush="#FFDDDDDD" 
                          BorderThickness="1" 
                          CornerRadius="5"
                          Padding="20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" 
                                     Text="أكثر المنتجات مبيعاً" 
                                     FontSize="18" 
                                     FontWeight="Bold"
                                     Foreground="#FF2196F3"
                                     Margin="0,0,0,15"/>

                            <ListBox Grid.Row="1" 
                                   ItemsSource="{Binding TopSellingProducts}"
                                   BorderThickness="0"
                                   Background="Transparent">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Border Padding="10,8" 
                                              BorderBrush="#FFEEEEEE" 
                                              BorderThickness="0,0,0,1">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding ProductName}" 
                                                             FontWeight="Bold" 
                                                             FontSize="12"/>
                                                    <TextBlock Text="{Binding QuantitySold, StringFormat='الكمية: {0}'}" 
                                                             Foreground="Gray" 
                                                             FontSize="10"/>
                                                </StackPanel>

                                                <TextBlock Grid.Column="1" 
                                                         Text="{Binding TotalRevenue, StringFormat='{}{0:N0} ج.م'}" 
                                                         FontSize="12" 
                                                         FontWeight="Bold"
                                                         Foreground="#FF4CAF50"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Grid>
                    </Border>
                </Grid>

                <!-- Detailed Tables -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Recent Sales -->
                    <Border Grid.Column="0" 
                          Background="White" 
                          BorderBrush="#FFDDDDDD" 
                          BorderThickness="1" 
                          CornerRadius="5"
                          Padding="20"
                          Margin="0,0,10,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" 
                                     Text="المبيعات الأخيرة" 
                                     FontSize="18" 
                                     FontWeight="Bold"
                                     Foreground="#FF2196F3"
                                     Margin="0,0,0,15"/>

                            <DataGrid Grid.Row="1"
                                    ItemsSource="{Binding SalesData}"
                                    AutoGenerateColumns="False"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    GridLinesVisibility="Horizontal"
                                    HeadersVisibility="Column"
                                    BorderThickness="0"
                                    Background="White"
                                    MaxHeight="300">
                                
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="التاريخ" Binding="{Binding SaleDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="80"/>
                                    <DataGridTextColumn Header="المبلغ" Binding="{Binding TotalAmount, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                                    <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="80"/>
                                    <DataGridTextColumn Header="الكاشير" Binding="{Binding CashierName}" Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </Border>

                    <!-- Top Customers -->
                    <Border Grid.Column="1" 
                          Background="White" 
                          BorderBrush="#FFDDDDDD" 
                          BorderThickness="1" 
                          CornerRadius="5"
                          Padding="20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" 
                                     Text="أفضل العملاء" 
                                     FontSize="18" 
                                     FontWeight="Bold"
                                     Foreground="#FF2196F3"
                                     Margin="0,0,0,15"/>

                            <ListBox Grid.Row="1" 
                                   ItemsSource="{Binding TopCustomers}"
                                   BorderThickness="0"
                                   Background="Transparent"
                                   MaxHeight="300">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Border Padding="10,8" 
                                              BorderBrush="#FFEEEEEE" 
                                              BorderThickness="0,0,0,1">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding CustomerName}" 
                                                             FontWeight="Bold" 
                                                             FontSize="12"/>
                                                    <TextBlock Text="{Binding TransactionCount, StringFormat='عدد المعاملات: {0}'}" 
                                                             Foreground="Gray" 
                                                             FontSize="10"/>
                                                </StackPanel>

                                                <TextBlock Grid.Column="1" 
                                                         Text="{Binding TotalPurchases, StringFormat='{}{0:N0} ج.م'}" 
                                                         FontSize="12" 
                                                         FontWeight="Bold"
                                                         Foreground="#FF2196F3"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Grid>
                    </Border>
                </Grid>
            </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
