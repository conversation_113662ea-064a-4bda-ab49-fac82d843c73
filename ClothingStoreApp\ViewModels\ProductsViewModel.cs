using System.Collections.ObjectModel;
using System.Windows.Input;
using ClothingStoreApp.Models;
using ClothingStoreApp.Services;
using ClothingStoreApp.Data;
using Microsoft.Win32;
using System.IO;

namespace ClothingStoreApp.ViewModels
{
    public class ProductsViewModel : BaseViewModel
    {
        private readonly ProductService _productService;
        private ObservableCollection<Product> _products;
        private ObservableCollection<Product> _filteredProducts;
        private Product? _selectedProduct;
        private string _searchText = string.Empty;
        private bool _isAddingProduct = false;
        private bool _isEditingProduct = false;

        // New/Edit Product Properties
        private string _productName = string.Empty;
        private string _productDescription = string.Empty;
        private decimal _productPrice = 0;
        private decimal _productCostPrice = 0;
        private int _productQuantity = 0;
        private int _productMinimumStock = 5;
        private string _productCategory = string.Empty;
        private string _productBrand = string.Empty;
        private string _productSize = string.Empty;
        private string _productColor = string.Empty;
        private string _productBarcode = string.Empty;
        private string _productImagePath = string.Empty;
        private bool _isSaving = false;
        private bool _isLoading = false;

        public ProductsViewModel()
        {
            try
            {
                var context = new ClothingStoreContext();
                // Ensure database is created
                context.Database.EnsureCreated();
                _productService = new ProductService(context);

                _products = new ObservableCollection<Product>();
                _filteredProducts = new ObservableCollection<Product>();

                // Initialize commands
                LoadProductsCommand = new RelayCommand(async () => await LoadProductsAsync());
                AddProductCommand = new RelayCommand(() => StartAddProduct());
                EditProductCommand = new RelayCommand(() => StartEditProduct(), () => SelectedProduct != null);
                DeleteProductCommand = new RelayCommand(async () => await DeleteProductAsync(), () => SelectedProduct != null);
                SaveProductCommand = new RelayCommand(async () => await SaveProductAsync());
                CancelProductCommand = new RelayCommand(() => CancelProductOperation());
                SearchCommand = new RelayCommand(() => FilterProducts());
                SelectImageCommand = new RelayCommand(() => SelectProductImage());
                GenerateBarcodeCommand = new RelayCommand(() => GenerateBarcode());

                // Load products on startup
                _ = Task.Run(LoadProductsAsync);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing ProductsViewModel: {ex.Message}");
            }
        }

        #region Properties

        public ObservableCollection<Product> Products
        {
            get => _products;
            set => SetProperty(ref _products, value);
        }

        public ObservableCollection<Product> FilteredProducts
        {
            get => _filteredProducts;
            set => SetProperty(ref _filteredProducts, value);
        }

        public Product? SelectedProduct
        {
            get => _selectedProduct;
            set => SetProperty(ref _selectedProduct, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                FilterProducts();
            }
        }

        public bool IsAddingProduct
        {
            get => _isAddingProduct;
            set => SetProperty(ref _isAddingProduct, value);
        }

        public bool IsEditingProduct
        {
            get => _isEditingProduct;
            set => SetProperty(ref _isEditingProduct, value);
        }

        public bool IsProductFormVisible => IsAddingProduct || IsEditingProduct;

        // Product Form Properties
        public string ProductName
        {
            get => _productName;
            set => SetProperty(ref _productName, value);
        }

        public string ProductDescription
        {
            get => _productDescription;
            set => SetProperty(ref _productDescription, value);
        }

        public decimal ProductPrice
        {
            get => _productPrice;
            set => SetProperty(ref _productPrice, value);
        }

        public decimal ProductCostPrice
        {
            get => _productCostPrice;
            set => SetProperty(ref _productCostPrice, value);
        }

        public int ProductQuantity
        {
            get => _productQuantity;
            set => SetProperty(ref _productQuantity, value);
        }

        public int ProductMinimumStock
        {
            get => _productMinimumStock;
            set => SetProperty(ref _productMinimumStock, value);
        }

        public string ProductCategory
        {
            get => _productCategory;
            set => SetProperty(ref _productCategory, value);
        }

        public string ProductBrand
        {
            get => _productBrand;
            set => SetProperty(ref _productBrand, value);
        }

        public string ProductSize
        {
            get => _productSize;
            set => SetProperty(ref _productSize, value);
        }

        public string ProductColor
        {
            get => _productColor;
            set => SetProperty(ref _productColor, value);
        }

        public string ProductBarcode
        {
            get => _productBarcode;
            set => SetProperty(ref _productBarcode, value);
        }

        public string ProductImagePath
        {
            get => _productImagePath;
            set => SetProperty(ref _productImagePath, value);
        }

        #endregion

        #region Commands

        public ICommand LoadProductsCommand { get; }
        public ICommand AddProductCommand { get; }
        public ICommand EditProductCommand { get; }
        public ICommand DeleteProductCommand { get; }
        public ICommand SaveProductCommand { get; }
        public ICommand CancelProductCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand SelectImageCommand { get; }
        public ICommand GenerateBarcodeCommand { get; }

        #endregion

        #region Methods

        private async Task LoadProductsAsync()
        {
            if (_isLoading) return; // Prevent multiple loads

            try
            {
                _isLoading = true;
                if (_productService == null) return;

                var products = await _productService.GetAllProductsAsync();
                
                Products.Clear();
                foreach (var product in products)
                {
                    Products.Add(product);
                }
                
                FilterProducts();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading products: {ex.Message}");
            }
            finally
            {
                _isLoading = false; // Reset the flag
            }
        }

        private void FilterProducts()
        {
            FilteredProducts.Clear();
            
            var filtered = string.IsNullOrWhiteSpace(SearchText) 
                ? Products 
                : Products.Where(p => 
                    p.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Category.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Brand.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Barcode.Contains(SearchText, StringComparison.OrdinalIgnoreCase));

            foreach (var product in filtered)
            {
                FilteredProducts.Add(product);
            }
        }

        private void StartAddProduct()
        {
            ClearProductForm();
            IsAddingProduct = true;
            OnPropertyChanged(nameof(IsProductFormVisible));
        }

        private void StartEditProduct()
        {
            if (SelectedProduct == null) return;

            LoadProductToForm(SelectedProduct);
            IsEditingProduct = true;
            OnPropertyChanged(nameof(IsProductFormVisible));
        }

        private async Task SaveProductAsync()
        {
            if (_isSaving) return; // Prevent multiple saves

            try
            {
                _isSaving = true;
                if (_productService == null) return;

                bool isAdding = IsAddingProduct;
                var product = isAdding ? new Product() : SelectedProduct;
                if (product == null) return;

                // Update product properties
                product.Name = ProductName;
                product.Description = ProductDescription;
                product.Price = ProductPrice;
                product.CostPrice = ProductCostPrice;
                product.Quantity = ProductQuantity;
                product.MinimumStock = ProductMinimumStock;
                product.Category = ProductCategory;
                product.Brand = ProductBrand;
                product.Size = ProductSize;
                product.Color = ProductColor;
                product.Barcode = ProductBarcode;
                product.ImagePath = ProductImagePath;

                if (isAdding)
                {
                    product.CreatedDate = DateTime.Now;
                    product.IsActive = true;
                    var savedProduct = await _productService.AddProductAsync(product);
                    Products.Add(savedProduct);
                }
                else
                {
                    product.UpdatedDate = DateTime.Now;
                    await _productService.UpdateProductAsync(product);
                }

                FilterProducts();
                CancelProductOperation();

                // Show success message
                System.Windows.MessageBox.Show(
                    isAdding ? "تم إضافة المنتج بنجاح!" : "تم تحديث المنتج بنجاح!",
                    "نجح الحفظ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving product: {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء حفظ المنتج: {ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                _isSaving = false; // Reset the flag
            }
        }

        private async Task DeleteProductAsync()
        {
            if (SelectedProduct == null || _productService == null) return;

            try
            {
                await _productService.DeleteProductAsync(SelectedProduct.Id);
                Products.Remove(SelectedProduct);
                FilterProducts();
                SelectedProduct = null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting product: {ex.Message}");
            }
        }

        private void CancelProductOperation()
        {
            IsAddingProduct = false;
            IsEditingProduct = false;
            ClearProductForm();
            OnPropertyChanged(nameof(IsProductFormVisible));
        }

        private void ClearProductForm()
        {
            ProductName = string.Empty;
            ProductDescription = string.Empty;
            ProductPrice = 0;
            ProductCostPrice = 0;
            ProductQuantity = 0;
            ProductMinimumStock = 5;
            ProductCategory = string.Empty;
            ProductBrand = string.Empty;
            ProductSize = string.Empty;
            ProductColor = string.Empty;
            ProductBarcode = string.Empty;
            ProductImagePath = string.Empty;
        }

        private void LoadProductToForm(Product product)
        {
            ProductName = product.Name;
            ProductDescription = product.Description;
            ProductPrice = product.Price;
            ProductCostPrice = product.CostPrice;
            ProductQuantity = product.Quantity;
            ProductMinimumStock = product.MinimumStock;
            ProductCategory = product.Category;
            ProductBrand = product.Brand;
            ProductSize = product.Size;
            ProductColor = product.Color;
            ProductBarcode = product.Barcode;
            ProductImagePath = product.ImagePath;
        }

        private void SelectProductImage()
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Image files (*.jpg, *.jpeg, *.png, *.bmp)|*.jpg;*.jpeg;*.png;*.bmp",
                Title = "اختر صورة المنتج"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                // Copy image to app directory
                var appDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "ClothingStore", "Images");
                Directory.CreateDirectory(appDir);
                
                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(openFileDialog.FileName)}";
                var destPath = Path.Combine(appDir, fileName);
                
                File.Copy(openFileDialog.FileName, destPath, true);
                ProductImagePath = destPath;
            }
        }

        private async void GenerateBarcode()
        {
            try
            {
                string barcode;
                bool isUnique = false;
                int attempts = 0;

                do
                {
                    // Generate a barcode with format: CS + 8 digits
                    var random = new Random().Next(10000000, 99999999);
                    barcode = $"CS{random}";

                    // Check if barcode already exists
                    if (_productService != null)
                    {
                        var existingProduct = await _productService.GetProductByBarcodeAsync(barcode);
                        isUnique = existingProduct == null;
                    }
                    else
                    {
                        isUnique = true;
                    }

                    attempts++;
                } while (!isUnique && attempts < 10);

                ProductBarcode = barcode;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating barcode: {ex.Message}");
                // Fallback to simple timestamp-based barcode
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                ProductBarcode = $"CS{timestamp}";
            }
        }

        #endregion
    }
}
