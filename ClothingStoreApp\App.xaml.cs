﻿using System.Configuration;
using System.Data;
using System.Windows;
using ClothingStoreApp.Data;
using Microsoft.EntityFrameworkCore;

namespace ClothingStoreApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // Initialize database
        await InitializeDatabaseAsync();
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            using var context = new ClothingStoreContext();
            await context.Database.EnsureCreatedAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إعداد قاعدة البيانات: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }
}

