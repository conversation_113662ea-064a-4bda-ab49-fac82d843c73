using Microsoft.EntityFrameworkCore;
using ClothingStoreApp.Data;
using ClothingStoreApp.Models;

namespace ClothingStoreApp.Services
{
    public class SalesService
    {
        private readonly ClothingStoreContext _context;
        private readonly ProductService _productService;

        public SalesService(ClothingStoreContext context, ProductService productService)
        {
            _context = context;
            _productService = productService;
        }

        public async Task<Sale> CreateSaleAsync(Sale sale)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Add the sale
                _context.Sales.Add(sale);
                await _context.SaveChangesAsync();

                // Update product quantities
                foreach (var item in sale.SaleItems)
                {
                    await _productService.ReduceStockAsync(item.ProductId, item.Quantity);
                }

                // Update customer total purchases if customer exists
                if (sale.CustomerId.HasValue)
                {
                    var customer = await _context.Customers.FindAsync(sale.CustomerId.Value);
                    if (customer != null)
                    {
                        customer.TotalPurchases += sale.TotalAmount;
                        customer.LoyaltyPoints += (int)(sale.TotalAmount / 10); // 1 point per 10 EGP
                        customer.LastVisit = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return sale;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<List<Sale>> GetSalesByDateAsync(DateTime date)
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => s.SaleDate.Date == date.Date)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }

        public async Task<List<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => s.SaleDate.Date >= startDate.Date && s.SaleDate.Date <= endDate.Date)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }

        public async Task<Sale?> GetSaleByIdAsync(int id)
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<decimal> GetTotalSalesForDateAsync(DateTime date)
        {
            return await _context.Sales
                .Where(s => s.SaleDate.Date == date.Date && !s.IsReturned)
                .SumAsync(s => s.TotalAmount);
        }

        public async Task<decimal> GetTotalSalesForMonthAsync(int year, int month)
        {
            return await _context.Sales
                .Where(s => s.SaleDate.Year == year && s.SaleDate.Month == month && !s.IsReturned)
                .SumAsync(s => s.TotalAmount);
        }

        public async Task<int> GetSalesCountForDateAsync(DateTime date)
        {
            return await _context.Sales
                .CountAsync(s => s.SaleDate.Date == date.Date && !s.IsReturned);
        }

        public async Task<List<Product>> GetTopSellingProductsAsync(int count = 10)
        {
            var topProductIds = await _context.SaleItems
                .Include(si => si.Sale)
                .Where(si => !si.Sale.IsReturned)
                .GroupBy(si => si.ProductId)
                .OrderByDescending(g => g.Sum(si => si.Quantity))
                .Take(count)
                .Select(g => g.Key)
                .ToListAsync();

            return await _context.Products
                .Where(p => topProductIds.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<bool> ReturnSaleAsync(int saleId, string reason)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var sale = await GetSaleByIdAsync(saleId);
                if (sale == null || sale.IsReturned) return false;

                // Mark sale as returned
                sale.IsReturned = true;
                sale.ReturnDate = DateTime.Now;
                sale.Notes += $" - مرتجع: {reason}";

                // Restore product quantities
                foreach (var item in sale.SaleItems)
                {
                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        product.Quantity += item.Quantity;
                        product.UpdatedDate = DateTime.Now;
                    }
                }

                // Update customer total purchases if customer exists
                if (sale.CustomerId.HasValue)
                {
                    var customer = await _context.Customers.FindAsync(sale.CustomerId.Value);
                    if (customer != null)
                    {
                        customer.TotalPurchases -= sale.TotalAmount;
                        customer.LoyaltyPoints -= (int)(sale.TotalAmount / 10);
                        if (customer.LoyaltyPoints < 0) customer.LoyaltyPoints = 0;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
