﻿<Application x:Class="ClothingStoreApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ClothingStoreApp"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:helpers="clr-namespace:ClothingStoreApp.Helpers"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Teal" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <helpers:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>

            <!-- Custom Colors for Clothing Store Theme -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#FF009688"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FFFF9800"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#FF4CAF50"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FFF5F5F5"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFFFF"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#FFF44336"/>

            <!-- Custom Styles -->
            <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            </Style>

            <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Margin" Value="0,0,0,16"/>
            </Style>

            <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Margin" Value="0,0,0,8"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
