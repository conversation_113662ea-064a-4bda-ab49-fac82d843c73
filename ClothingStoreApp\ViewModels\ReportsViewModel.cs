using System.Collections.ObjectModel;
using System.Windows.Input;
using ClothingStoreApp.Models;
using ClothingStoreApp.Services;
using ClothingStoreApp.Data;
using System.Windows;

namespace ClothingStoreApp.ViewModels
{
    public class ReportsViewModel : BaseViewModel
    {
        private readonly SalesService _salesService;
        private readonly ProductService _productService;
        private readonly CustomerService _customerService;

        // Date Range
        private DateTime _startDate = DateTime.Today.AddDays(-30);
        private DateTime _endDate = DateTime.Today;

        // Sales Reports
        private ObservableCollection<Sale> _salesData;
        private decimal _totalSales = 0;
        private decimal _totalProfit = 0;
        private int _totalTransactions = 0;
        private decimal _averageTransactionValue = 0;

        // Product Reports
        private ObservableCollection<ProductSalesReport> _topSellingProducts;
        private ObservableCollection<Product> _lowStockProducts;
        private int _totalProductsSold = 0;

        // Customer Reports
        private ObservableCollection<CustomerSalesReport> _topCustomers;
        private int _totalCustomers = 0;
        private int _newCustomersThisPeriod = 0;

        // Daily Sales Chart Data
        private ObservableCollection<DailySalesData> _dailySalesData;

        // Report Type Selection
        private string _selectedReportType = "المبيعات";

        public ReportsViewModel()
        {
            try
            {
                var context = new ClothingStoreContext();
                // Ensure database is created
                context.Database.EnsureCreated();
                _salesService = new SalesService(context, new ProductService(context));
                _productService = new ProductService(context);
                _customerService = new CustomerService(context);

                _salesData = new ObservableCollection<Sale>();
                _topSellingProducts = new ObservableCollection<ProductSalesReport>();
                _lowStockProducts = new ObservableCollection<Product>();
                _topCustomers = new ObservableCollection<CustomerSalesReport>();
                _dailySalesData = new ObservableCollection<DailySalesData>();

                // Initialize commands
                GenerateReportCommand = new RelayCommand(async () => await GenerateReportAsync());
                ExportReportCommand = new RelayCommand(async () => await ExportReportAsync());
                RefreshDataCommand = new RelayCommand(async () => await RefreshDataAsync());

                // Load initial data
                _ = Task.Run(GenerateReportAsync);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing ReportsViewModel: {ex.Message}");
            }
        }

        #region Properties

        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        public ObservableCollection<Sale> SalesData
        {
            get => _salesData;
            set => SetProperty(ref _salesData, value);
        }

        public decimal TotalSales
        {
            get => _totalSales;
            set => SetProperty(ref _totalSales, value);
        }

        public decimal TotalProfit
        {
            get => _totalProfit;
            set => SetProperty(ref _totalProfit, value);
        }

        public int TotalTransactions
        {
            get => _totalTransactions;
            set => SetProperty(ref _totalTransactions, value);
        }

        public decimal AverageTransactionValue
        {
            get => _averageTransactionValue;
            set => SetProperty(ref _averageTransactionValue, value);
        }

        public ObservableCollection<ProductSalesReport> TopSellingProducts
        {
            get => _topSellingProducts;
            set => SetProperty(ref _topSellingProducts, value);
        }

        public ObservableCollection<Product> LowStockProducts
        {
            get => _lowStockProducts;
            set => SetProperty(ref _lowStockProducts, value);
        }

        public int TotalProductsSold
        {
            get => _totalProductsSold;
            set => SetProperty(ref _totalProductsSold, value);
        }

        public ObservableCollection<CustomerSalesReport> TopCustomers
        {
            get => _topCustomers;
            set => SetProperty(ref _topCustomers, value);
        }

        public int TotalCustomers
        {
            get => _totalCustomers;
            set => SetProperty(ref _totalCustomers, value);
        }

        public int NewCustomersThisPeriod
        {
            get => _newCustomersThisPeriod;
            set => SetProperty(ref _newCustomersThisPeriod, value);
        }

        public ObservableCollection<DailySalesData> DailySalesData
        {
            get => _dailySalesData;
            set => SetProperty(ref _dailySalesData, value);
        }

        public string SelectedReportType
        {
            get => _selectedReportType;
            set => SetProperty(ref _selectedReportType, value);
        }

        public List<string> ReportTypes => new List<string>
        {
            "المبيعات",
            "المنتجات",
            "العملاء",
            "المخزون"
        };

        #endregion

        #region Commands

        public ICommand GenerateReportCommand { get; }
        public ICommand ExportReportCommand { get; }
        public ICommand RefreshDataCommand { get; }

        #endregion

        #region Methods

        private async Task GenerateReportAsync()
        {
            try
            {
                await LoadSalesDataAsync();
                await LoadProductDataAsync();
                await LoadCustomerDataAsync();
                await LoadDailySalesDataAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating report: {ex.Message}");
            }
        }

        private async Task LoadSalesDataAsync()
        {
            try
            {
                if (_salesService == null) return;

                var sales = await _salesService.GetSalesByDateRangeAsync(StartDate, EndDate);
                
                SalesData.Clear();
                foreach (var sale in sales.OrderByDescending(s => s.SaleDate))
                {
                    SalesData.Add(sale);
                }

                // Calculate totals
                TotalSales = sales.Sum(s => s.TotalAmount);
                TotalProfit = sales.Sum(s => s.TotalAmount - s.SaleItems.Sum(si => si.Quantity * (si.Product?.CostPrice ?? 0)));
                TotalTransactions = sales.Count;
                AverageTransactionValue = TotalTransactions > 0 ? TotalSales / TotalTransactions : 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading sales data: {ex.Message}");
            }
        }

        private async Task LoadProductDataAsync()
        {
            try
            {
                if (_productService == null || _salesService == null) return;

                // Get all products
                var products = await _productService.GetAllProductsAsync();
                
                // Get sales data for the period
                var sales = await _salesService.GetSalesByDateRangeAsync(StartDate, EndDate);
                
                // Calculate product sales
                var productSales = sales
                    .SelectMany(s => s.SaleItems)
                    .GroupBy(si => si.ProductId)
                    .Select(g => new ProductSalesReport
                    {
                        ProductId = g.Key,
                        ProductName = g.First().Product?.Name ?? "Unknown",
                        QuantitySold = g.Sum(si => si.Quantity),
                        TotalRevenue = g.Sum(si => si.TotalPrice),
                        TotalProfit = g.Sum(si => si.TotalPrice - (si.Quantity * (si.Product?.CostPrice ?? 0)))
                    })
                    .OrderByDescending(p => p.QuantitySold)
                    .Take(10)
                    .ToList();

                TopSellingProducts.Clear();
                foreach (var product in productSales)
                {
                    TopSellingProducts.Add(product);
                }

                // Low stock products
                var lowStock = products.Where(p => p.Quantity <= p.MinimumStock).Take(10);
                LowStockProducts.Clear();
                foreach (var product in lowStock)
                {
                    LowStockProducts.Add(product);
                }

                TotalProductsSold = productSales.Sum(p => p.QuantitySold);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading product data: {ex.Message}");
            }
        }

        private async Task LoadCustomerDataAsync()
        {
            try
            {
                if (_customerService == null || _salesService == null) return;

                var customers = await _customerService.GetAllCustomersAsync();
                var sales = await _salesService.GetSalesByDateRangeAsync(StartDate, EndDate);

                // Top customers by sales
                var customerSales = sales
                    .Where(s => s.CustomerId.HasValue)
                    .GroupBy(s => s.CustomerId.Value)
                    .Select(g => new CustomerSalesReport
                    {
                        CustomerId = g.Key,
                        CustomerName = customers.FirstOrDefault(c => c.Id == g.Key)?.Name ?? "Unknown",
                        TotalPurchases = g.Sum(s => s.TotalAmount),
                        TransactionCount = g.Count(),
                        AverageTransactionValue = g.Average(s => s.TotalAmount)
                    })
                    .OrderByDescending(c => c.TotalPurchases)
                    .Take(10)
                    .ToList();

                TopCustomers.Clear();
                foreach (var customer in customerSales)
                {
                    TopCustomers.Add(customer);
                }

                TotalCustomers = customers.Count;
                NewCustomersThisPeriod = customers.Count(c => c.CreatedDate >= StartDate && c.CreatedDate <= EndDate);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading customer data: {ex.Message}");
            }
        }

        private async Task LoadDailySalesDataAsync()
        {
            try
            {
                if (_salesService == null) return;

                var sales = await _salesService.GetSalesByDateRangeAsync(StartDate, EndDate);
                
                var dailySales = sales
                    .GroupBy(s => s.SaleDate.Date)
                    .Select(g => new DailySalesData
                    {
                        Date = g.Key,
                        TotalSales = g.Sum(s => s.TotalAmount),
                        TransactionCount = g.Count()
                    })
                    .OrderBy(d => d.Date)
                    .ToList();

                DailySalesData.Clear();
                foreach (var day in dailySales)
                {
                    DailySalesData.Add(day);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading daily sales data: {ex.Message}");
            }
        }

        private async Task RefreshDataAsync()
        {
            await GenerateReportAsync();
        }

        private async Task ExportReportAsync()
        {
            try
            {
                // TODO: Implement export functionality (Excel, PDF, etc.)
                System.Diagnostics.Debug.WriteLine("Export functionality to be implemented");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting report: {ex.Message}");
            }
        }

        #endregion
    }

    // Helper classes for reports
    public class ProductSalesReport
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int QuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalProfit { get; set; }
    }

    public class CustomerSalesReport
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalPurchases { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
    }

    public class DailySalesData
    {
        public DateTime Date { get; set; }
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
    }
}
