﻿<Window x:Class="ClothingStoreApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ClothingStoreApp"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewmodels="clr-namespace:ClothingStoreApp.ViewModels"
        xmlns:System="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="نظام إدارة محل الملابس"
        Height="800" Width="1200"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.DataContext>
        <viewmodels:MainViewModel/>
    </Window.DataContext>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:ColorZone Grid.Row="0"
                                    Mode="PrimaryMid"
                                    Padding="16"
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Store"
                                               Width="32" Height="32"
                                               VerticalAlignment="Center"
                                               Foreground="White"/>
                        <TextBlock Text="نظام إدارة محل الملابس"
                                 FontSize="24"
                                 FontWeight="Bold"
                                 VerticalAlignment="Center"
                                 Margin="16,0,0,0"
                                 Foreground="White"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <TextBlock Text="{Binding Source={x:Static System:DateTime.Now}, StringFormat='yyyy/MM/dd'}"
                                 VerticalAlignment="Center"
                                 Margin="0,0,16,0"
                                 Foreground="White"
                                 FontSize="14"/>
                        <materialDesign:PackIcon Kind="Calendar"
                                               Width="20" Height="20"
                                               VerticalAlignment="Center"
                                               Foreground="White"/>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Navigation Menu -->
                <materialDesign:Card Grid.Column="0"
                                   Margin="8,8,4,8"
                                   materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="0,16">
                            <!-- Dashboard -->
                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                  Command="{Binding ShowDashboardCommand}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Margin="8,2">
                                <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                    <materialDesign:PackIcon Kind="ViewDashboard"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="لوحة التحكم"
                                             Margin="8,0,0,0"
                                             VerticalAlignment="Center"
                                             FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <!-- Products -->
                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                  Command="{Binding ShowProductsCommand}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Margin="8,2">
                                <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                    <materialDesign:PackIcon Kind="Tshirt"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="المنتجات"
                                             Margin="8,0,0,0"
                                             VerticalAlignment="Center"
                                             FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <!-- Sales -->
                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                  Command="{Binding ShowSalesCommand}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Margin="8,2">
                                <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                    <materialDesign:PackIcon Kind="CashRegister"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="نقاط البيع"
                                             Margin="8,0,0,0"
                                             VerticalAlignment="Center"
                                             FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <!-- Customers -->
                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                  Command="{Binding ShowCustomersCommand}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Margin="8,2">
                                <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                    <materialDesign:PackIcon Kind="AccountGroup"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="العملاء"
                                             Margin="8,0,0,0"
                                             VerticalAlignment="Center"
                                             FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <!-- Reports -->
                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                  Command="{Binding ShowReportsCommand}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Margin="8,2">
                                <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                    <materialDesign:PackIcon Kind="ChartLine"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="التقارير"
                                             Margin="8,0,0,0"
                                             VerticalAlignment="Center"
                                             FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="16,8"/>

                            <!-- Settings -->
                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                  Command="{Binding ShowSettingsCommand}"
                                  HorizontalAlignment="Stretch"
                                  HorizontalContentAlignment="Right"
                                  Padding="16,12"
                                  Margin="8,2">
                                <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                    <materialDesign:PackIcon Kind="Settings"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="الإعدادات"
                                             Margin="8,0,0,0"
                                             VerticalAlignment="Center"
                                             FontSize="14"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>

                <!-- Content Area -->
                <materialDesign:Card Grid.Column="1"
                                   Margin="4,8,8,8"
                                   materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <Grid>
                        <!-- Dashboard View -->
                        <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Dashboard}">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Margin="24">
                                    <TextBlock Text="لوحة التحكم"
                                             Style="{StaticResource HeaderTextStyle}"/>

                                    <!-- Statistics Cards -->
                                    <Grid Margin="0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Today's Sales -->
                                        <materialDesign:Card Grid.Column="0"
                                                           Style="{StaticResource CardStyle}"
                                                           Background="{StaticResource PrimaryBrush}">
                                            <StackPanel>
                                                <materialDesign:PackIcon Kind="CashMultiple"
                                                                       Width="32" Height="32"
                                                                       HorizontalAlignment="Center"
                                                                       Foreground="White"/>
                                                <TextBlock Text="{Binding TodaysSales, StringFormat='{}{0:N2} ج.م'}"
                                                         FontSize="20"
                                                         FontWeight="Bold"
                                                         HorizontalAlignment="Center"
                                                         Foreground="White"
                                                         Margin="0,8,0,4"/>
                                                <TextBlock Text="مبيعات اليوم"
                                                         HorizontalAlignment="Center"
                                                         Foreground="White"
                                                         FontSize="12"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <!-- Sales Count -->
                                        <materialDesign:Card Grid.Column="1"
                                                           Style="{StaticResource CardStyle}"
                                                           Background="{StaticResource SecondaryBrush}">
                                            <StackPanel>
                                                <materialDesign:PackIcon Kind="Receipt"
                                                                       Width="32" Height="32"
                                                                       HorizontalAlignment="Center"
                                                                       Foreground="White"/>
                                                <TextBlock Text="{Binding TodaysSalesCount}"
                                                         FontSize="20"
                                                         FontWeight="Bold"
                                                         HorizontalAlignment="Center"
                                                         Foreground="White"
                                                         Margin="0,8,0,4"/>
                                                <TextBlock Text="عدد الفواتير"
                                                         HorizontalAlignment="Center"
                                                         Foreground="White"
                                                         FontSize="12"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <!-- Low Stock -->
                                        <materialDesign:Card Grid.Column="2"
                                                           Style="{StaticResource CardStyle}"
                                                           Background="{StaticResource ErrorBrush}">
                                            <StackPanel>
                                                <materialDesign:PackIcon Kind="AlertCircle"
                                                                       Width="32" Height="32"
                                                                       HorizontalAlignment="Center"
                                                                       Foreground="White"/>
                                                <TextBlock Text="{Binding LowStockCount}"
                                                         FontSize="20"
                                                         FontWeight="Bold"
                                                         HorizontalAlignment="Center"
                                                         Foreground="White"
                                                         Margin="0,8,0,4"/>
                                                <TextBlock Text="منتجات قاربت على النفاد"
                                                         HorizontalAlignment="Center"
                                                         Foreground="White"
                                                         FontSize="12"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <!-- Total Customers -->
                                        <materialDesign:Card Grid.Column="3"
                                                           Style="{StaticResource CardStyle}"
                                                           Background="{StaticResource AccentBrush}">
                                            <StackPanel>
                                                <materialDesign:PackIcon Kind="AccountGroup"
                                                                       Width="32" Height="32"
                                                                       HorizontalAlignment="Center"
                                                                       Foreground="White"/>
                                                <TextBlock Text="{Binding TotalCustomers}"
                                                         FontSize="20"
                                                         FontWeight="Bold"
                                                         HorizontalAlignment="Center"
                                                         Foreground="White"
                                                         Margin="0,8,0,4"/>
                                                <TextBlock Text="إجمالي العملاء"
                                                         HorizontalAlignment="Center"
                                                         Foreground="White"
                                                         FontSize="12"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </Grid>

                                    <!-- Quick Actions -->
                                    <TextBlock Text="الإجراءات السريعة"
                                             Style="{StaticResource SubHeaderTextStyle}"
                                             Margin="0,32,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <Button Grid.Column="0"
                                              Style="{StaticResource MaterialDesignRaisedButton}"
                                              Command="{Binding ShowSalesCommand}"
                                              Height="60"
                                              Margin="8">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Plus"
                                                                       Width="20" Height="20"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="بيع جديد"
                                                         Margin="8,0,0,0"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>

                                        <Button Grid.Column="1"
                                              Style="{StaticResource MaterialDesignRaisedButton}"
                                              Command="{Binding ShowProductsCommand}"
                                              Height="60"
                                              Margin="8">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Plus"
                                                                       Width="20" Height="20"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="منتج جديد"
                                                         Margin="8,0,0,0"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>

                                        <Button Grid.Column="2"
                                              Style="{StaticResource MaterialDesignRaisedButton}"
                                              Command="{Binding ShowCustomersCommand}"
                                              Height="60"
                                              Margin="8">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Plus"
                                                                       Width="20" Height="20"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="عميل جديد"
                                                         Margin="8,0,0,0"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </StackPanel>
                            </ScrollViewer>
                        </Grid>

                        <!-- Other Views (Placeholder) -->
                        <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Products}">
                            <TextBlock Text="صفحة المنتجات - قيد التطوير"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     FontSize="18"
                                     Foreground="Gray"/>
                        </Grid>

                        <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Sales}">
                            <TextBlock Text="صفحة نقاط البيع - قيد التطوير"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     FontSize="18"
                                     Foreground="Gray"/>
                        </Grid>

                        <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Customers}">
                            <TextBlock Text="صفحة العملاء - قيد التطوير"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     FontSize="18"
                                     Foreground="Gray"/>
                        </Grid>

                        <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Reports}">
                            <TextBlock Text="صفحة التقارير - قيد التطوير"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     FontSize="18"
                                     Foreground="Gray"/>
                        </Grid>

                        <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Settings}">
                            <TextBlock Text="صفحة الإعدادات - قيد التطوير"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     FontSize="18"
                                     Foreground="Gray"/>
                        </Grid>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Status Bar -->
            <materialDesign:ColorZone Grid.Row="2"
                                    Mode="PrimaryDark"
                                    Padding="16,8"
                                    materialDesign:ShadowAssist.ShadowDepth="Depth1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                             Text="جاهز للاستخدام"
                             VerticalAlignment="Center"
                             Foreground="White"
                             FontSize="12"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="الإصدار 1.0.0"
                                 VerticalAlignment="Center"
                                 Foreground="White"
                                 FontSize="12"
                                 Margin="0,0,16,0"/>
                        <materialDesign:PackIcon Kind="Information"
                                               Width="16" Height="16"
                                               VerticalAlignment="Center"
                                               Foreground="White"/>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>
