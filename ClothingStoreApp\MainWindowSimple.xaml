<Window x:Class="ClothingStoreApp.MainWindowSimple"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:viewmodels="clr-namespace:ClothingStoreApp.ViewModels"
        xmlns:System="clr-namespace:System;assembly=mscorlib"
        Title="نظام إدارة محل الملابس"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">
    
    <Window.DataContext>
        <viewmodels:MainViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF2196F3" Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                         Text="نظام إدارة محل الملابس" 
                         FontSize="24" 
                         FontWeight="Bold"
                         VerticalAlignment="Center"
                         Foreground="White"/>

                <TextBlock Grid.Column="1"
                         Text="{Binding Source={x:Static System:DateTime.Now}, StringFormat='yyyy/MM/dd'}"
                         VerticalAlignment="Center"
                         Foreground="White"
                         FontSize="14"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <Border Grid.Column="0" Background="#FFF5F5F5" BorderBrush="#FFDDDDDD" BorderThickness="0,0,1,0">
                <StackPanel Margin="10">
                    <Button Content="📊 لوحة التحكم" 
                          Command="{Binding ShowDashboardCommand}"
                          Height="40" Margin="0,5"
                          HorizontalContentAlignment="Right"
                          Background="#FF4CAF50" Foreground="White"
                          BorderThickness="0"/>
                    
                    <Button Content="👕 المنتجات" 
                          Command="{Binding ShowProductsCommand}"
                          Height="40" Margin="0,5"
                          HorizontalContentAlignment="Right"
                          Background="#FF2196F3" Foreground="White"
                          BorderThickness="0"/>
                    
                    <Button Content="💰 نقاط البيع" 
                          Command="{Binding ShowSalesCommand}"
                          Height="40" Margin="0,5"
                          HorizontalContentAlignment="Right"
                          Background="#FFFF9800" Foreground="White"
                          BorderThickness="0"/>
                    
                    <Button Content="👥 العملاء" 
                          Command="{Binding ShowCustomersCommand}"
                          Height="40" Margin="0,5"
                          HorizontalContentAlignment="Right"
                          Background="#FF9C27B0" Foreground="White"
                          BorderThickness="0"/>
                    
                    <Button Content="📈 التقارير" 
                          Command="{Binding ShowReportsCommand}"
                          Height="40" Margin="0,5"
                          HorizontalContentAlignment="Right"
                          Background="#FF607D8B" Foreground="White"
                          BorderThickness="0"/>
                    
                    <Button Content="⚙️ الإعدادات" 
                          Command="{Binding ShowSettingsCommand}"
                          Height="40" Margin="0,5"
                          HorizontalContentAlignment="Right"
                          Background="#FF795548" Foreground="White"
                          BorderThickness="0"/>
                </StackPanel>
            </Border>

            <!-- Content Area -->
            <Border Grid.Column="1" Background="White" Padding="20">
                <Grid>
                    <!-- Dashboard View -->
                    <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Dashboard}">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <TextBlock Text="لوحة التحكم" 
                                     FontSize="28" 
                                     FontWeight="Bold"
                                     Foreground="#FF2196F3"
                                     Margin="0,0,0,20"/>

                            <!-- Statistics Cards -->
                            <Grid Margin="0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Today's Sales -->
                                <Border Grid.Column="0" Grid.Row="0" 
                                      Background="#FF4CAF50" 
                                      Margin="10" Padding="20" CornerRadius="5">
                                    <StackPanel>
                                        <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="{Binding TodaysSales, StringFormat='{}{0:N2} ج.م'}" 
                                                 FontSize="24" 
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="White"
                                                 Margin="0,0,0,5"/>
                                        <TextBlock Text="مبيعات اليوم" 
                                                 HorizontalAlignment="Center"
                                                 Foreground="White"
                                                 FontSize="14"/>
                                    </StackPanel>
                                </Border>

                                <!-- Sales Count -->
                                <Border Grid.Column="1" Grid.Row="0" 
                                      Background="#FFFF9800" 
                                      Margin="10" Padding="20" CornerRadius="5">
                                    <StackPanel>
                                        <TextBlock Text="🧾" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="{Binding TodaysSalesCount}" 
                                                 FontSize="24" 
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="White"
                                                 Margin="0,0,0,5"/>
                                        <TextBlock Text="عدد الفواتير" 
                                                 HorizontalAlignment="Center"
                                                 Foreground="White"
                                                 FontSize="14"/>
                                    </StackPanel>
                                </Border>

                                <!-- Low Stock -->
                                <Border Grid.Column="0" Grid.Row="1" 
                                      Background="#FFF44336" 
                                      Margin="10" Padding="20" CornerRadius="5">
                                    <StackPanel>
                                        <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="{Binding LowStockCount}" 
                                                 FontSize="24" 
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="White"
                                                 Margin="0,0,0,5"/>
                                        <TextBlock Text="منتجات قاربت على النفاد" 
                                                 HorizontalAlignment="Center"
                                                 Foreground="White"
                                                 FontSize="14"/>
                                    </StackPanel>
                                </Border>

                                <!-- Total Customers -->
                                <Border Grid.Column="1" Grid.Row="1" 
                                      Background="#FF9C27B0" 
                                      Margin="10" Padding="20" CornerRadius="5">
                                    <StackPanel>
                                        <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="{Binding TotalCustomers}" 
                                                 FontSize="24" 
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="White"
                                                 Margin="0,0,0,5"/>
                                        <TextBlock Text="إجمالي العملاء" 
                                                 HorizontalAlignment="Center"
                                                 Foreground="White"
                                                 FontSize="14"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- Quick Actions -->
                            <TextBlock Text="الإجراءات السريعة" 
                                     FontSize="20" 
                                     FontWeight="Bold"
                                     Foreground="#FF2196F3"
                                     Margin="0,30,0,15"/>

                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="➕ بيع جديد"
                                      Command="{Binding ShowSalesCommand}"
                                      Height="50" Width="120"
                                      Margin="10"
                                      Background="#FF4CAF50" Foreground="White"
                                      BorderThickness="0"/>

                                <Button Content="➕ منتج جديد"
                                      Command="{Binding ShowProductsCommand}"
                                      Height="50" Width="120"
                                      Margin="10"
                                      Background="#FF2196F3" Foreground="White"
                                      BorderThickness="0"/>

                                <Button Content="➕ عميل جديد"
                                      Command="{Binding ShowCustomersCommand}"
                                      Height="50" Width="120"
                                      Margin="10"
                                      Background="#FF9C27B0" Foreground="White"
                                      BorderThickness="0"/>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>

                <!-- Other Views (Placeholder) -->
                <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Products}">
                    <TextBlock Text="صفحة المنتجات - قيد التطوير" 
                             HorizontalAlignment="Center" 
                             VerticalAlignment="Center"
                             FontSize="24"
                             Foreground="Gray"/>
                </Grid>

                <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Sales}">
                    <TextBlock Text="صفحة نقاط البيع - قيد التطوير" 
                             HorizontalAlignment="Center" 
                             VerticalAlignment="Center"
                             FontSize="24"
                             Foreground="Gray"/>
                </Grid>

                <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Customers}">
                    <TextBlock Text="صفحة العملاء - قيد التطوير" 
                             HorizontalAlignment="Center" 
                             VerticalAlignment="Center"
                             FontSize="24"
                             Foreground="Gray"/>
                </Grid>

                <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Reports}">
                    <TextBlock Text="صفحة التقارير - قيد التطوير" 
                             HorizontalAlignment="Center" 
                             VerticalAlignment="Center"
                             FontSize="24"
                             Foreground="Gray"/>
                </Grid>

                <Grid Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Settings}">
                    <TextBlock Text="صفحة الإعدادات - قيد التطوير" 
                             HorizontalAlignment="Center" 
                             VerticalAlignment="Center"
                             FontSize="24"
                             Foreground="Gray"/>
                </Grid>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#FF37474F" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                         Text="جاهز للاستخدام"
                         VerticalAlignment="Center"
                         Foreground="White"
                         FontSize="12"/>

                <TextBlock Grid.Column="1"
                         Text="الإصدار 1.0.0"
                         VerticalAlignment="Center"
                         Foreground="White"
                         FontSize="12"/>
            </Grid>
        </Border>
    </Grid>
</Window>
