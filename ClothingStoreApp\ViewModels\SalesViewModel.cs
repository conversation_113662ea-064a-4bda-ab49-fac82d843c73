using System.Collections.ObjectModel;
using System.Windows.Input;
using ClothingStoreApp.Models;
using ClothingStoreApp.Services;
using ClothingStoreApp.Data;
using System.Windows;

namespace ClothingStoreApp.ViewModels
{
    public class SalesViewModel : BaseViewModel
    {
        private readonly ProductService _productService;
        private readonly SalesService _salesService;
        private readonly CustomerService _customerService;

        private ObservableCollection<Product> _products;
        private ObservableCollection<Product> _filteredProducts;
        private ObservableCollection<SaleItem> _currentSaleItems;
        private Product? _selectedProduct;
        private Customer? _selectedCustomer;
        private string _productSearchText = string.Empty;
        private string _customerSearchText = string.Empty;
        private string _barcodeInput = string.Empty;
        private int _selectedQuantity = 1;
        private decimal _discountAmount = 0;
        private decimal _taxRate = 0; // Tax rate as percentage
        private string _paymentMethod = "نقدي";
        private string _notes = string.Empty;

        // Calculated properties
        private decimal _subtotal = 0;
        private decimal _taxAmount = 0;
        private decimal _totalAmount = 0;

        public SalesViewModel()
        {
            try
            {
                var context = new ClothingStoreContext();
                _productService = new ProductService(context);
                _salesService = new SalesService(context, _productService);
                _customerService = new CustomerService(context);

                _products = new ObservableCollection<Product>();
                _filteredProducts = new ObservableCollection<Product>();
                _currentSaleItems = new ObservableCollection<SaleItem>();

                // Initialize commands
                LoadProductsCommand = new RelayCommand(async () => await LoadProductsAsync());
                SearchProductsCommand = new RelayCommand(() => FilterProducts());
                SearchCustomersCommand = new RelayCommand(async () => await SearchCustomersAsync());
                AddToCartCommand = new RelayCommand(() => AddToCart(), () => SelectedProduct != null);
                RemoveFromCartCommand = new RelayCommand<SaleItem>(item => RemoveFromCart(item));
                UpdateQuantityCommand = new RelayCommand<SaleItem>(item => UpdateItemQuantity(item));
                ProcessSaleCommand = new RelayCommand(async () => await ProcessSaleAsync(), () => CurrentSaleItems.Any());
                ClearSaleCommand = new RelayCommand(() => ClearCurrentSale());
                ScanBarcodeCommand = new RelayCommand(async () => await ScanBarcodeAsync());

                // Load initial data
                _ = Task.Run(LoadProductsAsync);

                // Subscribe to collection changes to update totals
                _currentSaleItems.CollectionChanged += (s, e) => CalculateTotals();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing SalesViewModel: {ex.Message}");
            }
        }

        #region Properties

        public ObservableCollection<Product> Products
        {
            get => _products;
            set => SetProperty(ref _products, value);
        }

        public ObservableCollection<Product> FilteredProducts
        {
            get => _filteredProducts;
            set => SetProperty(ref _filteredProducts, value);
        }

        public ObservableCollection<SaleItem> CurrentSaleItems
        {
            get => _currentSaleItems;
            set => SetProperty(ref _currentSaleItems, value);
        }

        public Product? SelectedProduct
        {
            get => _selectedProduct;
            set => SetProperty(ref _selectedProduct, value);
        }

        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set => SetProperty(ref _selectedCustomer, value);
        }

        public string ProductSearchText
        {
            get => _productSearchText;
            set
            {
                SetProperty(ref _productSearchText, value);
                FilterProducts();
            }
        }

        public string CustomerSearchText
        {
            get => _customerSearchText;
            set => SetProperty(ref _customerSearchText, value);
        }

        public string BarcodeInput
        {
            get => _barcodeInput;
            set => SetProperty(ref _barcodeInput, value);
        }

        public int SelectedQuantity
        {
            get => _selectedQuantity;
            set => SetProperty(ref _selectedQuantity, Math.Max(1, value));
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                SetProperty(ref _discountAmount, Math.Max(0, value));
                CalculateTotals();
            }
        }

        public decimal TaxRate
        {
            get => _taxRate;
            set
            {
                SetProperty(ref _taxRate, Math.Max(0, Math.Min(100, value)));
                CalculateTotals();
            }
        }

        public string PaymentMethod
        {
            get => _paymentMethod;
            set => SetProperty(ref _paymentMethod, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public decimal Subtotal
        {
            get => _subtotal;
            set => SetProperty(ref _subtotal, value);
        }

        public decimal TaxAmount
        {
            get => _taxAmount;
            set => SetProperty(ref _taxAmount, value);
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set => SetProperty(ref _totalAmount, value);
        }

        public List<string> PaymentMethods => new List<string>
        {
            "نقدي",
            "فيزا",
            "ماستركارد",
            "آجل"
        };

        #endregion

        #region Commands

        public ICommand LoadProductsCommand { get; }
        public ICommand SearchProductsCommand { get; }
        public ICommand SearchCustomersCommand { get; }
        public ICommand AddToCartCommand { get; }
        public ICommand RemoveFromCartCommand { get; }
        public ICommand UpdateQuantityCommand { get; }
        public ICommand ProcessSaleCommand { get; }
        public ICommand ClearSaleCommand { get; }
        public ICommand ScanBarcodeCommand { get; }

        #endregion

        #region Methods

        private async Task LoadProductsAsync()
        {
            try
            {
                if (_productService == null) return;

                var products = await _productService.GetAllProductsAsync();
                
                Products.Clear();
                foreach (var product in products.Where(p => p.Quantity > 0))
                {
                    Products.Add(product);
                }
                
                FilterProducts();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading products: {ex.Message}");
            }
        }

        private void FilterProducts()
        {
            FilteredProducts.Clear();
            
            var filtered = string.IsNullOrWhiteSpace(ProductSearchText) 
                ? Products.Take(20) // Show only first 20 for performance
                : Products.Where(p => 
                    p.Name.Contains(ProductSearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Barcode.Contains(ProductSearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Category.Contains(ProductSearchText, StringComparison.OrdinalIgnoreCase))
                    .Take(20);

            foreach (var product in filtered)
            {
                FilteredProducts.Add(product);
            }
        }

        private async Task SearchCustomersAsync()
        {
            try
            {
                if (_customerService == null || string.IsNullOrWhiteSpace(CustomerSearchText)) 
                {
                    SelectedCustomer = null;
                    return;
                }

                var customers = await _customerService.SearchCustomersAsync(CustomerSearchText);
                SelectedCustomer = customers.FirstOrDefault();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error searching customers: {ex.Message}");
            }
        }

        private void AddToCart()
        {
            if (SelectedProduct == null || SelectedQuantity <= 0) return;

            // Check if product already exists in cart
            var existingItem = CurrentSaleItems.FirstOrDefault(item => item.ProductId == SelectedProduct.Id);
            
            if (existingItem != null)
            {
                // Update quantity
                existingItem.Quantity += SelectedQuantity;
                existingItem.TotalPrice = existingItem.Quantity * existingItem.UnitPrice;
            }
            else
            {
                // Add new item
                var saleItem = new SaleItem
                {
                    ProductId = SelectedProduct.Id,
                    Product = SelectedProduct,
                    Quantity = SelectedQuantity,
                    UnitPrice = SelectedProduct.Price,
                    TotalPrice = SelectedQuantity * SelectedProduct.Price
                };
                
                CurrentSaleItems.Add(saleItem);
            }

            // Reset selection
            SelectedQuantity = 1;
            CalculateTotals();
        }

        private void RemoveFromCart(SaleItem? item)
        {
            if (item != null)
            {
                CurrentSaleItems.Remove(item);
                CalculateTotals();
            }
        }

        private void UpdateItemQuantity(SaleItem? item)
        {
            if (item != null && item.Quantity > 0)
            {
                item.TotalPrice = item.Quantity * item.UnitPrice;
                CalculateTotals();
            }
        }

        private void CalculateTotals()
        {
            Subtotal = CurrentSaleItems.Sum(item => item.TotalPrice);
            TaxAmount = (Subtotal - DiscountAmount) * (TaxRate / 100);
            TotalAmount = Subtotal - DiscountAmount + TaxAmount;
        }

        private async Task ProcessSaleAsync()
        {
            try
            {
                if (!CurrentSaleItems.Any() || _salesService == null) return;

                var sale = new Sale
                {
                    SaleDate = DateTime.Now,
                    SubTotal = Subtotal,
                    DiscountAmount = DiscountAmount,
                    TaxAmount = TaxAmount,
                    TotalAmount = TotalAmount,
                    PaymentMethod = PaymentMethod,
                    CustomerId = SelectedCustomer?.Id,
                    Notes = Notes,
                    CashierName = Environment.UserName,
                    SaleItems = CurrentSaleItems.ToList()
                };

                await _salesService.CreateSaleAsync(sale);
                
                // Clear current sale
                ClearCurrentSale();
                
                // Reload products to update quantities
                await LoadProductsAsync();
                
                // TODO: Show success message and print receipt
                System.Diagnostics.Debug.WriteLine($"Sale completed successfully. Total: {TotalAmount:C}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error processing sale: {ex.Message}");
                // TODO: Show error message to user
            }
        }

        private void ClearCurrentSale()
        {
            CurrentSaleItems.Clear();
            SelectedCustomer = null;
            CustomerSearchText = string.Empty;
            DiscountAmount = 0;
            TaxRate = 0;
            PaymentMethod = "نقدي";
            Notes = string.Empty;
            CalculateTotals();
        }

        private async Task ScanBarcodeAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(BarcodeInput) || _productService == null) return;

                var product = await _productService.GetProductByBarcodeAsync(BarcodeInput);
                if (product != null && product.Quantity > 0)
                {
                    SelectedProduct = product;
                    AddToCart();
                }

                BarcodeInput = string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error scanning barcode: {ex.Message}");
            }
        }

        #endregion
    }

    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Func<T, bool>? _canExecute;

        public RelayCommand(Action<T> execute, Func<T, bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke((T)parameter!) ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute((T)parameter!);
        }
    }
}
