<UserControl x:Class="ClothingStoreApp.Views.SalesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:viewmodels="clr-namespace:ClothingStoreApp.ViewModels"
             FlowDirection="RightToLeft">
    
    <UserControl.DataContext>
        <viewmodels:SalesViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                     Text="نقاط البيع" 
                     FontSize="28" 
                     FontWeight="Bold"
                     Foreground="#FF2196F3"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="🔄 تحديث المنتجات"
                      Command="{Binding LoadProductsCommand}"
                      Height="40" Width="140"
                      Background="#FF4CAF50" Foreground="White"
                      BorderThickness="0"
                      Margin="5,0"/>
                
                <Button Content="🗑️ مسح الفاتورة"
                      Command="{Binding ClearSaleCommand}"
                      Height="40" Width="120"
                      Background="#FFF44336" Foreground="White"
                      BorderThickness="0"
                      Margin="5,0"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Products and Search -->
            <Grid Grid.Column="0" Margin="0,0,10,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Barcode Scanner -->
                <Border Grid.Row="0" 
                      Background="White" 
                      BorderBrush="#FFDDDDDD" 
                      BorderThickness="1" 
                      CornerRadius="5"
                      Padding="15"
                      Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                               Text="{Binding BarcodeInput, UpdateSourceTrigger=PropertyChanged}"
                               FontSize="16"
                               Height="40"
                               Padding="10"
                               BorderBrush="#FFDDDDDD"
                               BorderThickness="1"
                               Margin="0,0,10,0"/>

                        <Button Grid.Column="1"
                              Content="📷 مسح"
                              Command="{Binding ScanBarcodeCommand}"
                              Height="40" Width="80"
                              Background="#FF2196F3" Foreground="White"
                              BorderThickness="0"/>
                    </Grid>
                </Border>

                <!-- Product Search -->
                <Border Grid.Row="1" 
                      Background="White" 
                      BorderBrush="#FFDDDDDD" 
                      BorderThickness="1" 
                      CornerRadius="5"
                      Padding="15"
                      Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                               Text="{Binding ProductSearchText, UpdateSourceTrigger=PropertyChanged}"
                               FontSize="14"
                               Height="35"
                               Padding="10"
                               BorderBrush="#FFDDDDDD"
                               BorderThickness="1"
                               Margin="0,0,10,0"/>

                        <TextBox Grid.Column="1"
                               Text="{Binding SelectedQuantity}"
                               FontSize="14"
                               Height="35"
                               Padding="10"
                               BorderBrush="#FFDDDDDD"
                               BorderThickness="1"
                               HorizontalContentAlignment="Center"
                               Margin="0,0,10,0"/>

                        <Button Grid.Column="2"
                              Content="➕ إضافة"
                              Command="{Binding AddToCartCommand}"
                              Height="35" Width="80"
                              Background="#FF4CAF50" Foreground="White"
                              BorderThickness="0"/>
                    </Grid>
                </Border>

                <!-- Products List -->
                <Border Grid.Row="2" 
                      Background="White" 
                      BorderBrush="#FFDDDDDD" 
                      BorderThickness="1" 
                      CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <Border Grid.Row="0" 
                              Background="#FFF5F5F5" 
                              BorderBrush="#FFDDDDDD" 
                              BorderThickness="0,0,0,1"
                              Padding="15,10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="اسم المنتج" FontWeight="Bold"/>
                                <TextBlock Grid.Column="1" Text="السعر" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Grid.Column="2" Text="الكمية" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Grid.Column="3" Text="الفئة" FontWeight="Bold" HorizontalAlignment="Center"/>
                            </Grid>
                        </Border>

                        <!-- Products List -->
                        <ListBox Grid.Row="1"
                               ItemsSource="{Binding FilteredProducts}"
                               SelectedItem="{Binding SelectedProduct}"
                               BorderThickness="0"
                               Background="White">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border Padding="15,10" 
                                          BorderBrush="#FFEEEEEE" 
                                          BorderThickness="0,0,0,1">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="100"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding Description}" 
                                                         Foreground="Gray" 
                                                         FontSize="12"
                                                         TextTrimming="CharacterEllipsis"/>
                                            </StackPanel>
                                            
                                            <TextBlock Grid.Column="1" 
                                                     Text="{Binding Price, StringFormat='{}{0:N2} ج.م'}" 
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"/>
                                            
                                            <TextBlock Grid.Column="2" 
                                                     Text="{Binding Quantity}" 
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"/>
                                            
                                            <TextBlock Grid.Column="3" 
                                                     Text="{Binding Category}" 
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     FontSize="12"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Grid>
                </Border>
            </Grid>

            <!-- Right Panel - Cart and Checkout -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Customer Selection -->
                <Border Grid.Row="0" 
                      Background="White" 
                      BorderBrush="#FFDDDDDD" 
                      BorderThickness="1" 
                      CornerRadius="5"
                      Padding="15"
                      Margin="0,0,0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" 
                                 Text="العميل:" 
                                 FontWeight="Bold" 
                                 Margin="0,0,0,5"/>

                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox Grid.Column="0"
                                    ItemsSource="{Binding Customers}"
                                    SelectedItem="{Binding SelectedCustomer}"
                                    DisplayMemberPath="Name"
                                    Height="35"
                                    Padding="10"
                                    BorderBrush="#FFDDDDDD"
                                    BorderThickness="1"
                                    Margin="0,0,0,5"/>

                            <Button Grid.Column="2"
                                  Content="🔄"
                                  Command="{Binding LoadCustomersCommand}"
                                  Height="35" Width="35"
                                  Background="#FF2196F3" Foreground="White"
                                  BorderThickness="0"
                                  ToolTip="تحديث قائمة العملاء"/>
                        </Grid>

                        <TextBlock Grid.Row="2"
                                 Text="{Binding SelectedCustomer.Name, StringFormat='العميل: {0}'}"
                                 Foreground="#FF4CAF50"
                                 FontSize="12"
                                 Visibility="{Binding SelectedCustomer, Converter={StaticResource NullToVisibilityConverter}}"/>
                    </Grid>
                </Border>

                <!-- Shopping Cart -->
                <Border Grid.Row="1" 
                      Background="White" 
                      BorderBrush="#FFDDDDDD" 
                      BorderThickness="1" 
                      CornerRadius="5"
                      Margin="0,0,0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Cart Header -->
                        <Border Grid.Row="0" 
                              Background="#FFF5F5F5" 
                              BorderBrush="#FFDDDDDD" 
                              BorderThickness="0,0,0,1"
                              Padding="15,10">
                            <TextBlock Text="سلة المشتريات" FontWeight="Bold"/>
                        </Border>

                        <!-- Cart Items -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding CurrentSaleItems}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Padding="15,10" 
                                              BorderBrush="#FFEEEEEE" 
                                              BorderThickness="0,0,0,1">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <Grid Grid.Row="0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Column="0" 
                                                             Text="{Binding Product.Name}" 
                                                             FontWeight="Bold"
                                                             TextTrimming="CharacterEllipsis"/>

                                                    <Button Grid.Column="1"
                                                          Content="❌"
                                                          Command="{Binding DataContext.RemoveFromCartCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                          CommandParameter="{Binding}"
                                                          Width="25" Height="25"
                                                          Background="Transparent"
                                                          BorderThickness="0"
                                                          Foreground="Red"
                                                          FontSize="12"/>
                                                </Grid>

                                                <Grid Grid.Row="1" Margin="0,5,0,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="60"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="80"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBox Grid.Column="0"
                                                           Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                                                           Height="25"
                                                           Padding="5"
                                                           BorderBrush="#FFDDDDDD"
                                                           BorderThickness="1"
                                                           HorizontalContentAlignment="Center"
                                                           FontSize="12"/>

                                                    <TextBlock Grid.Column="1" 
                                                             Text="{Binding UnitPrice, StringFormat='× {0:N2}'}" 
                                                             VerticalAlignment="Center"
                                                             HorizontalAlignment="Center"
                                                             FontSize="12"/>

                                                    <TextBlock Grid.Column="2"
                                                             Text="{Binding TotalPrice, StringFormat='{}{0:N2} ج.م'}"
                                                             VerticalAlignment="Center"
                                                             HorizontalAlignment="Left"
                                                             FontWeight="Bold"
                                                             FontSize="12"/>
                                                </Grid>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </Border>

                <!-- Checkout Panel -->
                <Border Grid.Row="2" 
                      Background="White" 
                      BorderBrush="#FFDDDDDD" 
                      BorderThickness="1" 
                      CornerRadius="5"
                      Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Discount -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="الخصم:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBox Grid.Column="1" 
                                   Text="{Binding DiscountAmount}"
                                   Height="30"
                                   Padding="5"
                                   BorderBrush="#FFDDDDDD"
                                   BorderThickness="1"/>
                        </Grid>

                        <!-- Tax Rate -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="الضريبة %:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBox Grid.Column="1" 
                                   Text="{Binding TaxRate}"
                                   Height="30"
                                   Padding="5"
                                   BorderBrush="#FFDDDDDD"
                                   BorderThickness="1"/>
                        </Grid>

                        <!-- Payment Method -->
                        <Grid Grid.Row="2" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="طريقة الدفع:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox Grid.Column="1" 
                                    SelectedItem="{Binding PaymentMethod}"
                                    ItemsSource="{Binding PaymentMethods}"
                                    Height="30"
                                    Padding="5"
                                    BorderBrush="#FFDDDDDD"
                                    BorderThickness="1"/>
                        </Grid>

                        <!-- Totals -->
                        <Separator Grid.Row="3" Margin="0,10"/>

                        <Grid Grid.Row="4" Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="المجموع الفرعي:" FontSize="14"/>
                            <TextBlock Grid.Column="1" Text="{Binding Subtotal, StringFormat='{}{0:N2} ج.م'}" FontSize="14"/>
                        </Grid>

                        <Grid Grid.Row="5" Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="الضريبة:" FontSize="14"/>
                            <TextBlock Grid.Column="1" Text="{Binding TaxAmount, StringFormat='{}{0:N2} ج.م'}" FontSize="14"/>
                        </Grid>

                        <Grid Grid.Row="6" Margin="0,5,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="الإجمالي:" FontSize="18" FontWeight="Bold" Foreground="#FF2196F3"/>
                            <TextBlock Grid.Column="1" Text="{Binding TotalAmount, StringFormat='{}{0:N2} ج.م'}" FontSize="18" FontWeight="Bold" Foreground="#FF2196F3"/>
                        </Grid>

                        <!-- Process Sale Button -->
                        <Button Grid.Row="7"
                              Content="💳 إتمام البيع"
                              Command="{Binding ProcessSaleCommand}"
                              Height="50"
                              Background="#FF4CAF50" Foreground="White"
                              BorderThickness="0"
                              FontSize="16"
                              FontWeight="Bold"/>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
