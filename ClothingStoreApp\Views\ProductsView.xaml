<UserControl x:Class="ClothingStoreApp.Views.ProductsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:viewmodels="clr-namespace:ClothingStoreApp.ViewModels"
             FlowDirection="RightToLeft">
    
    <UserControl.DataContext>
        <viewmodels:ProductsViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header and Search -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Title -->
            <TextBlock Grid.Row="0" 
                     Text="إدارة المنتجات" 
                     FontSize="28" 
                     FontWeight="Bold"
                     Foreground="#FF2196F3"
                     Margin="0,0,0,15"/>

            <!-- Search and Actions -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <Border Grid.Column="0" 
                      Background="White" 
                      BorderBrush="#FFDDDDDD" 
                      BorderThickness="1" 
                      CornerRadius="5"
                      Padding="10"
                      Margin="0,0,10,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0"
                               Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                               FontSize="14"
                               BorderThickness="0"
                               Background="Transparent"
                               VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Column="0"
                                 Text="البحث في المنتجات..."
                                 FontSize="14"
                                 Foreground="Gray"
                                 VerticalAlignment="Center"
                                 IsHitTestVisible="False">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding SearchText}" Value="">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                        
                        <TextBlock Grid.Column="1" 
                                 Text="🔍" 
                                 FontSize="16" 
                                 VerticalAlignment="Center"
                                 Foreground="Gray"/>
                    </Grid>
                </Border>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="➕ منتج جديد"
                          Command="{Binding AddProductCommand}"
                          Height="40" Width="120"
                          Background="#FF4CAF50" Foreground="White"
                          BorderThickness="0"
                          Margin="5,0"/>

                    <Button Content="✏️ تعديل"
                          Command="{Binding EditProductCommand}"
                          Height="40" Width="80"
                          Background="#FF2196F3" Foreground="White"
                          BorderThickness="0"
                          Margin="5,0"/>

                    <Button Content="🗑️ حذف"
                          Command="{Binding DeleteProductCommand}"
                          Height="40" Width="80"
                          Background="#FFF44336" Foreground="White"
                          BorderThickness="0"
                          Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Products List -->
            <Border Grid.Column="0" 
                  Background="White" 
                  BorderBrush="#FFDDDDDD" 
                  BorderThickness="1" 
                  CornerRadius="5"
                  Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- List Header -->
                    <Border Grid.Row="0" 
                          Background="#FFF5F5F5" 
                          BorderBrush="#FFDDDDDD" 
                          BorderThickness="0,0,0,1"
                          Padding="15,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="اسم المنتج" FontWeight="Bold"/>
                            <TextBlock Grid.Column="1" Text="السعر" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="الكمية" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="المقاس" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="الفئة" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Grid>
                    </Border>

                    <!-- Products DataGrid -->
                    <DataGrid Grid.Row="1"
                            ItemsSource="{Binding FilteredProducts}"
                            SelectedItem="{Binding SelectedProduct}"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="None"
                            BorderThickness="0"
                            Background="White"
                            AlternatingRowBackground="#FFF9F9F9">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="100"/>
                            <DataGridTextColumn Header="المقاس" Binding="{Binding Size}" Width="80"/>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="100"/>
                        </DataGrid.Columns>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Height" Value="40"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Quantity, Converter={StaticResource LowStockConverter}}" Value="True">
                                        <Setter Property="Background" Value="#FFFFE0E0"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- Product Details/Form -->
            <Border Grid.Column="1"
                  Background="White"
                  BorderBrush="#FFDDDDDD"
                  BorderThickness="1"
                  CornerRadius="5">

                <Grid>
                    <!-- Product Form -->
                <ScrollViewer VerticalScrollBarVisibility="Auto" 
                            Visibility="{Binding IsProductFormVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel Margin="20">
                        <TextBlock Text="{Binding IsAddingProduct, Converter={StaticResource AddEditTitleConverter}}" 
                                 FontSize="18" 
                                 FontWeight="Bold"
                                 Foreground="#FF2196F3"
                                 Margin="0,0,0,20"/>

                        <!-- Product Image -->
                        <Grid Margin="0,0,0,15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Text="صورة المنتج:" FontWeight="Bold" Margin="0,0,0,5"/>
                            
                            <Border Grid.Row="1" 
                                  Height="120" 
                                  Background="#FFF5F5F5" 
                                  BorderBrush="#FFDDDDDD" 
                                  BorderThickness="1"
                                  CornerRadius="5">
                                <Grid>
                                    <Image Source="{Binding ProductImagePath}" 
                                         Stretch="UniformToFill"
                                         Visibility="{Binding ProductImagePath, Converter={StaticResource StringToVisibilityConverter}}"/>
                                    
                                    <Button Content="📷 اختر صورة"
                                          Command="{Binding SelectImageCommand}"
                                          Background="Transparent"
                                          BorderThickness="0"
                                          Foreground="Gray"
                                          Visibility="{Binding ProductImagePath, Converter={StaticResource InverseStringToVisibilityConverter}}"/>
                                </Grid>
                            </Border>
                        </Grid>

                        <!-- Product Name -->
                        <Grid Margin="0,0,0,15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Text="اسم المنتج:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Grid.Row="1" 
                                   Text="{Binding ProductName}"
                                   Height="35"
                                   Padding="10,8"
                                   BorderBrush="#FFDDDDDD"
                                   BorderThickness="1"/>
                        </Grid>

                        <!-- Product Description -->
                        <Grid Margin="0,0,0,15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Text="الوصف:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Grid.Row="1" 
                                   Text="{Binding ProductDescription}"
                                   Height="60"
                                   Padding="10,8"
                                   BorderBrush="#FFDDDDDD"
                                   BorderThickness="1"
                                   TextWrapping="Wrap"
                                   AcceptsReturn="True"/>
                        </Grid>

                        <!-- Price and Cost -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Grid Grid.Column="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="سعر البيع:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding ProductPrice}"
                                       Height="35"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"/>
                            </Grid>
                            
                            <Grid Grid.Column="2">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="سعر التكلفة:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding ProductCostPrice}"
                                       Height="35"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"/>
                            </Grid>
                        </Grid>

                        <!-- Quantity and Minimum Stock -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Grid Grid.Column="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="الكمية:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding ProductQuantity}"
                                       Height="35"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"/>
                            </Grid>
                            
                            <Grid Grid.Column="2">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="الحد الأدنى:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding ProductMinimumStock}"
                                       Height="35"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"/>
                            </Grid>
                        </Grid>

                        <!-- Category and Brand -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Grid Grid.Column="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="الفئة:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox Grid.Row="1" 
                                        Text="{Binding ProductCategory}"
                                        Height="35"
                                        Padding="10,8"
                                        BorderBrush="#FFDDDDDD"
                                        BorderThickness="1"
                                        IsEditable="True">
                                    <ComboBoxItem Content="قمصان رجالي"/>
                                    <ComboBoxItem Content="قمصان نسائي"/>
                                    <ComboBoxItem Content="فساتين"/>
                                    <ComboBoxItem Content="بناطيل رجالي"/>
                                    <ComboBoxItem Content="بناطيل نسائي"/>
                                    <ComboBoxItem Content="ملابس أطفال"/>
                                    <ComboBoxItem Content="أحذية"/>
                                    <ComboBoxItem Content="إكسسوارات"/>
                                </ComboBox>
                            </Grid>
                            
                            <Grid Grid.Column="2">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="الماركة:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" 
                                       Text="{Binding ProductBrand}"
                                       Height="35"
                                       Padding="10,8"
                                       BorderBrush="#FFDDDDDD"
                                       BorderThickness="1"/>
                            </Grid>
                        </Grid>

                        <!-- Size and Color -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Grid Grid.Column="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="المقاس:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox Grid.Row="1" 
                                        Text="{Binding ProductSize}"
                                        Height="35"
                                        Padding="10,8"
                                        BorderBrush="#FFDDDDDD"
                                        BorderThickness="1"
                                        IsEditable="True">
                                    <ComboBoxItem Content="XS"/>
                                    <ComboBoxItem Content="S"/>
                                    <ComboBoxItem Content="M"/>
                                    <ComboBoxItem Content="L"/>
                                    <ComboBoxItem Content="XL"/>
                                    <ComboBoxItem Content="XXL"/>
                                    <ComboBoxItem Content="XXXL"/>
                                </ComboBox>
                            </Grid>
                            
                            <Grid Grid.Column="2">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="اللون:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox Grid.Row="1" 
                                        Text="{Binding ProductColor}"
                                        Height="35"
                                        Padding="10,8"
                                        BorderBrush="#FFDDDDDD"
                                        BorderThickness="1"
                                        IsEditable="True">
                                    <ComboBoxItem Content="أبيض"/>
                                    <ComboBoxItem Content="أسود"/>
                                    <ComboBoxItem Content="أزرق"/>
                                    <ComboBoxItem Content="أحمر"/>
                                    <ComboBoxItem Content="أخضر"/>
                                    <ComboBoxItem Content="أصفر"/>
                                    <ComboBoxItem Content="بني"/>
                                    <ComboBoxItem Content="رمادي"/>
                                    <ComboBoxItem Content="بنفسجي"/>
                                    <ComboBoxItem Content="وردي"/>
                                </ComboBox>
                            </Grid>
                        </Grid>

                        <!-- Barcode -->
                        <Grid Margin="0,0,0,20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Text="الباركود:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Grid.Row="1" 
                                   Text="{Binding ProductBarcode}"
                                   Height="35"
                                   Padding="10,8"
                                   BorderBrush="#FFDDDDDD"
                                   BorderThickness="1"/>
                        </Grid>

                        <!-- Action Buttons -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0"
                                  Content="💾 حفظ"
                                  Command="{Binding SaveProductCommand}"
                                  Height="40"
                                  Background="#FF4CAF50" Foreground="White"
                                  BorderThickness="0"/>

                            <Button Grid.Column="2"
                                  Content="❌ إلغاء"
                                  Command="{Binding CancelProductCommand}"
                                  Height="40"
                                  Background="#FF757575" Foreground="White"
                                  BorderThickness="0"/>
                        </Grid>
                    </StackPanel>
                </ScrollViewer>

                <!-- Product Details (when not editing) -->
                <ScrollViewer VerticalScrollBarVisibility="Auto" 
                            Visibility="{Binding IsProductFormVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <StackPanel Margin="20">
                        <TextBlock Text="تفاصيل المنتج" 
                                 FontSize="18" 
                                 FontWeight="Bold"
                                 Foreground="#FF2196F3"
                                 Margin="0,0,0,20"/>

                        <Grid Visibility="{Binding SelectedProduct, Converter={StaticResource NullToVisibilityConverter}}">
                            <!-- Product Image -->
                            <Border Height="150" 
                                  Background="#FFF5F5F5" 
                                  BorderBrush="#FFDDDDDD" 
                                  BorderThickness="1"
                                  CornerRadius="5"
                                  Margin="0,0,0,15">
                                <Image Source="{Binding SelectedProduct.ImagePath}" 
                                     Stretch="UniformToFill"/>
                            </Border>

                            <!-- Product Info -->
                            <StackPanel>
                                <TextBlock Text="{Binding SelectedProduct.Name}" 
                                         FontSize="16" 
                                         FontWeight="Bold"
                                         Margin="0,0,0,10"/>
                                
                                <TextBlock Text="{Binding SelectedProduct.Description}" 
                                         TextWrapping="Wrap"
                                         Foreground="Gray"
                                         Margin="0,0,0,15"/>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="السعر: " FontWeight="Bold"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedProduct.Price, StringFormat='{}{0:N2} ج.م'}"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="الكمية: " FontWeight="Bold"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedProduct.Quantity}"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="الفئة: " FontWeight="Bold"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedProduct.Category}"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="المقاس: " FontWeight="Bold"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedProduct.Size}"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="اللون: " FontWeight="Bold"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedProduct.Color}"/>
                                </Grid>
                            </StackPanel>
                        </Grid>

                        <TextBlock Text="اختر منتج لعرض التفاصيل" 
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center"
                                 Foreground="Gray"
                                 FontSize="16"
                                 Visibility="{Binding SelectedProduct, Converter={StaticResource NullToVisibilityConverter}, ConverterParameter=Inverse}"/>
                    </StackPanel>
                </ScrollViewer>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
