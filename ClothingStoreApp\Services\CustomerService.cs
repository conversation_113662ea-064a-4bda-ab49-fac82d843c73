using Microsoft.EntityFrameworkCore;
using ClothingStoreApp.Data;
using ClothingStoreApp.Models;

namespace ClothingStoreApp.Services
{
    public class CustomerService
    {
        private readonly ClothingStoreContext _context;

        public CustomerService(ClothingStoreContext context)
        {
            _context = context;
        }

        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            return await _context.Customers
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            return await _context.Customers
                .Include(c => c.Sales)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<Customer?> GetCustomerByPhoneAsync(string phone)
        {
            return await _context.Customers
                .FirstOrDefaultAsync(c => c.Phone == phone && c.IsActive);
        }

        public async Task<List<Customer>> SearchCustomersAsync(string searchTerm)
        {
            return await _context.Customers
                .Where(c => c.IsActive && 
                           (c.Name.Contains(searchTerm) || 
                            c.Phone.Contains(searchTerm) ||
                            c.Email.Contains(searchTerm)))
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Customer> AddCustomerAsync(Customer customer)
        {
            _context.Customers.Add(customer);
            await _context.SaveChangesAsync();
            return customer;
        }

        public async Task<Customer> UpdateCustomerAsync(Customer customer)
        {
            _context.Customers.Update(customer);
            await _context.SaveChangesAsync();
            return customer;
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            var customer = await _context.Customers.FindAsync(id);
            if (customer == null) return false;

            customer.IsActive = false;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Customer>> GetTopCustomersAsync(int count = 10)
        {
            return await _context.Customers
                .Where(c => c.IsActive)
                .OrderByDescending(c => c.TotalPurchases)
                .Take(count)
                .ToListAsync();
        }

        public async Task<List<Customer>> GetCustomersWithBirthdayAsync(DateTime date)
        {
            return await _context.Customers
                .Where(c => c.IsActive && 
                           c.DateOfBirth.Month == date.Month && 
                           c.DateOfBirth.Day == date.Day)
                .ToListAsync();
        }

        public async Task<bool> AddLoyaltyPointsAsync(int customerId, int points)
        {
            var customer = await _context.Customers.FindAsync(customerId);
            if (customer == null) return false;

            customer.LoyaltyPoints += points;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RedeemLoyaltyPointsAsync(int customerId, int points)
        {
            var customer = await _context.Customers.FindAsync(customerId);
            if (customer == null || customer.LoyaltyPoints < points) return false;

            customer.LoyaltyPoints -= points;
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
